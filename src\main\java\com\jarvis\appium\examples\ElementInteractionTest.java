package com.jarvis.appium.examples;

import java.io.*;
import java.util.concurrent.TimeUnit;

public class ElementInteractionTest {
    private static Process mcpServerProcess;
    private static BufferedReader serverOutput;
    private static BufferedWriter serverInput;
    private static BufferedReader serverError;
    
    public static void main(String[] args) {
        try {
            System.out.println("=== Element Interaction Test ===");
            
            // Start MCP server
            System.out.println("Starting MCP server...");
            ProcessBuilder pb = new ProcessBuilder("npx", "jarvis-appium");
            pb.directory(new File("../jarvis-appium"));
            pb.redirectErrorStream(false);
            mcpServerProcess = pb.start();
            
            serverInput = new BufferedWriter(new OutputStreamWriter(mcpServerProcess.getOutputStream()));
            serverOutput = new BufferedReader(new InputStreamReader(mcpServerProcess.getInputStream()));
            serverError = new BufferedReader(new InputStreamReader(mcpServerProcess.getErrorStream()));
            
            // Start server output reader thread
            Thread outputThread = new Thread(() -> {
                try {
                    String line;
                    while ((line = serverOutput.readLine()) != null) {
                        System.out.println("SERVER OUTPUT: " + line);
                    }
                } catch (IOException e) {
                    System.out.println("Error reading server output: " + e.getMessage());
                }
            });
            outputThread.setDaemon(true);
            outputThread.start();
            
            // Start server error reader thread
            Thread errorThread = new Thread(() -> {
                try {
                    String line;
                    while ((line = serverError.readLine()) != null) {
                        System.out.println("SERVER ERROR: " + line);
                    }
                } catch (IOException e) {
                    System.out.println("Error reading server error: " + e.getMessage());
                }
            });
            errorThread.setDaemon(true);
            errorThread.start();
            
            System.out.println("Waiting for server to start...");
            Thread.sleep(3000);
            
            System.out.println("Initializing MCP connection...");
            
            // Initialize MCP connection
            String initMessage = "{\"jsonrpc\":\"2.0\",\"id\":\"1\",\"method\":\"initialize\",\"params\":{\"protocolVersion\":\"2024-11-05\",\"clientInfo\":{\"name\":\"element-test\",\"version\":\"1.0.0\"},\"capabilities\":{}}}";
            System.out.println("SENDING: " + initMessage);
            serverInput.write(initMessage + "\n");
            serverInput.flush();
            Thread.sleep(1000);
            
            // Select platform
            System.out.println("Selecting Android platform...");
            String selectPlatformParams = "{\"jsonrpc\":\"2.0\",\"id\":\"2\",\"method\":\"tools/call\",\"params\":{\"name\":\"select_platform\",\"arguments\":{\"platform\":\"android\"}}}";
            System.out.println("SENDING: " + selectPlatformParams);
            serverInput.write(selectPlatformParams + "\n");
            serverInput.flush();
            Thread.sleep(1000);
            
            // Create session
            System.out.println("Creating session...");
            String createSessionParams = "{"
                + "\"jsonrpc\":\"2.0\",\"id\":\"3\",\"method\":\"tools/call\","
                + "\"params\":{"
                    + "\"name\":\"create_session\","
                    + "\"arguments\":{"
                        + "\"platform\":\"android\","
                        + "\"capabilities\":{"
                            + "\"platformName\":\"Android\","
                            + "\"automationName\":\"UiAutomator2\","
                            + "\"deviceName\":\"emulator-5554\","
                            + "\"app\":\"C:\\\\POC\\\\AI\\\\MCP Integration Server\\\\mcp-client-integration\\\\Android.SauceLabs.Mobile.Sample.app.2.7.1.apk\","
                            + "\"appPackage\":\"com.swaglabsmobileapp\","
                            + "\"appActivity\":\"com.swaglabsmobileapp.SplashActivity\","
                            + "\"noReset\":false,"
                            + "\"fullReset\":true"
                        + "}"
                    + "}"
                + "}"
            + "}";
            System.out.println("SENDING: " + createSessionParams);
            serverInput.write(createSessionParams + "\n");
            serverInput.flush();
            
            System.out.println("Waiting for session creation...");
            Thread.sleep(15000); // Wait for session to be created
            
            // Test element finding
            System.out.println("Testing element finding...");
            String findElementParams = "{"
                + "\"jsonrpc\":\"2.0\",\"id\":\"4\",\"method\":\"tools/call\","
                + "\"params\":{"
                    + "\"name\":\"find_element\","
                    + "\"arguments\":{"
                        + "\"strategy\":\"xpath\","
                        + "\"selector\":\"//*[@content-desc='test-LOGIN']\""
                    + "}"
                + "}"
            + "}";
            System.out.println("SENDING: " + findElementParams);
            serverInput.write(findElementParams + "\n");
            serverInput.flush();
            Thread.sleep(3000);
            
            // Test clicking element
            System.out.println("Testing element click...");
            String clickElementParams = "{"
                + "\"jsonrpc\":\"2.0\",\"id\":\"5\",\"method\":\"tools/call\","
                + "\"params\":{"
                    + "\"name\":\"click_element\","
                    + "\"arguments\":{"
                        + "\"strategy\":\"xpath\","
                        + "\"selector\":\"//*[@content-desc='test-LOGIN']\""
                    + "}"
                + "}"
            + "}";
            System.out.println("SENDING: " + clickElementParams);
            serverInput.write(clickElementParams + "\n");
            serverInput.flush();
            Thread.sleep(3000);
            
            // Test getting page source
            System.out.println("Testing get page source...");
            String getPageSourceParams = "{"
                + "\"jsonrpc\":\"2.0\",\"id\":\"6\",\"method\":\"tools/call\","
                + "\"params\":{"
                    + "\"name\":\"get_page_source\","
                    + "\"arguments\":{}"
                + "}"
            + "}";
            System.out.println("SENDING: " + getPageSourceParams);
            serverInput.write(getPageSourceParams + "\n");
            serverInput.flush();
            Thread.sleep(3000);
            
            System.out.println("Element interaction test completed!");
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            System.out.println("Cleaning up...");
            if (mcpServerProcess != null) {
                mcpServerProcess.destroyForcibly();
            }
        }
    }
}
