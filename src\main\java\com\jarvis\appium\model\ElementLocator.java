package com.jarvis.appium.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;

/**
 * Represents an element locator with multiple strategies
 */
public class ElementLocator {
    
    @JsonProperty("element")
    private String element;
    
    @JsonProperty("locators")
    private Map<String, String> locators;
    
    @JsonProperty("isClickable")
    private Boolean isClickable;
    
    @JsonProperty("isFocusable")
    private Boolean isFocusable;
    
    @JsonProperty("text")
    private String text;
    
    @JsonProperty("contentDescription")
    private String contentDescription;
    
    @JsonProperty("className")
    private String className;
    
    @JsonProperty("bounds")
    private String bounds;
    
    public ElementLocator() {}
    
    // Getters and setters
    public String getElement() {
        return element;
    }
    
    public void setElement(String element) {
        this.element = element;
    }
    
    public Map<String, String> getLocators() {
        return locators;
    }
    
    public void setLocators(Map<String, String> locators) {
        this.locators = locators;
    }
    
    public Boolean getIsClickable() {
        return isClickable;
    }
    
    public void setIsClickable(Boolean isClickable) {
        this.isClickable = isClickable;
    }
    
    public Boolean getIsFocusable() {
        return isFocusable;
    }
    
    public void setIsFocusable(Boolean isFocusable) {
        this.isFocusable = isFocusable;
    }
    
    public String getText() {
        return text;
    }
    
    public void setText(String text) {
        this.text = text;
    }
    
    public String getContentDescription() {
        return contentDescription;
    }
    
    public void setContentDescription(String contentDescription) {
        this.contentDescription = contentDescription;
    }
    
    public String getClassName() {
        return className;
    }
    
    public void setClassName(String className) {
        this.className = className;
    }
    
    public String getBounds() {
        return bounds;
    }
    
    public void setBounds(String bounds) {
        this.bounds = bounds;
    }
    
    /**
     * Get the best locator for the given strategy
     */
    public String getLocator(LocatorStrategy strategy) {
        if (locators == null) {
            return null;
        }
        return locators.get(strategy.getValue());
    }
    
    /**
     * Get the best locator for the platform using priority order
     */
    public LocatorInfo getBestLocator(Platform platform) {
        if (locators == null || locators.isEmpty()) {
            return null;
        }
        
        LocatorStrategy[] priorityOrder = platform == Platform.IOS ? 
            LocatorStrategy.getIosPriorityOrder() : 
            LocatorStrategy.getAndroidPriorityOrder();
        
        for (LocatorStrategy strategy : priorityOrder) {
            String locator = locators.get(strategy.getValue());
            if (locator != null && !locator.trim().isEmpty()) {
                return new LocatorInfo(strategy, locator);
            }
        }
        
        // Fallback to first available locator
        for (Map.Entry<String, String> entry : locators.entrySet()) {
            if (entry.getValue() != null && !entry.getValue().trim().isEmpty()) {
                try {
                    LocatorStrategy strategy = LocatorStrategy.fromString(entry.getKey());
                    return new LocatorInfo(strategy, entry.getValue());
                } catch (IllegalArgumentException e) {
                    // Skip unknown strategies
                }
            }
        }
        
        return null;
    }
    
    @Override
    public String toString() {
        return "ElementLocator{" +
                "element='" + element + '\'' +
                ", locators=" + locators +
                ", isClickable=" + isClickable +
                ", isFocusable=" + isFocusable +
                ", text='" + text + '\'' +
                ", contentDescription='" + contentDescription + '\'' +
                ", className='" + className + '\'' +
                ", bounds='" + bounds + '\'' +
                '}';
    }
    
    /**
     * Represents a locator strategy and value pair
     */
    public static class LocatorInfo {
        private final LocatorStrategy strategy;
        private final String value;
        
        public LocatorInfo(LocatorStrategy strategy, String value) {
            this.strategy = strategy;
            this.value = value;
        }
        
        public LocatorStrategy getStrategy() {
            return strategy;
        }
        
        public String getValue() {
            return value;
        }
        
        @Override
        public String toString() {
            return "LocatorInfo{" +
                    "strategy=" + strategy +
                    ", value='" + value + '\'' +
                    '}';
        }
    }
}
