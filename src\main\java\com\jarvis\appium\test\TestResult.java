package com.jarvis.appium.test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Represents the result of a test execution
 */
public class TestResult {
    
    private final String testName;
    private Status status;
    private long startTime;
    private long endTime;
    private final List<String> errors;
    private final Map<String, String> stepResults;
    private final Map<String, String> screenshots;
    private final Map<String, Object> data;
    
    public TestResult(String testName) {
        this.testName = testName;
        this.status = Status.RUNNING;
        this.errors = new ArrayList<>();
        this.stepResults = new HashMap<>();
        this.screenshots = new HashMap<>();
        this.data = new HashMap<>();
    }
    
    // Getters and setters
    public String getTestName() {
        return testName;
    }
    
    public Status getStatus() {
        return status;
    }
    
    public void setStatus(Status status) {
        this.status = status;
    }
    
    public long getStartTime() {
        return startTime;
    }
    
    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }
    
    public long getEndTime() {
        return endTime;
    }
    
    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }
    
    public long getDuration() {
        return endTime - startTime;
    }
    
    public List<String> getErrors() {
        return new ArrayList<>(errors);
    }
    
    public void addError(String step, String error) {
        errors.add(step + ": " + error);
    }
    
    public Map<String, String> getStepResults() {
        return new HashMap<>(stepResults);
    }
    
    public void addStepResult(String step, String result) {
        stepResults.put(step, result);
    }
    
    public Map<String, String> getScreenshots() {
        return new HashMap<>(screenshots);
    }
    
    public void addScreenshot(String name, String screenshot) {
        screenshots.put(name, screenshot);
    }
    
    public Map<String, Object> getData() {
        return new HashMap<>(data);
    }
    
    public void addData(String key, Object value) {
        data.put(key, value);
    }
    
    public boolean isSuccessful() {
        return status == Status.PASSED;
    }
    
    @Override
    public String toString() {
        return "TestResult{" +
                "testName='" + testName + '\'' +
                ", status=" + status +
                ", duration=" + getDuration() + "ms" +
                ", errors=" + errors.size() +
                ", stepResults=" + stepResults.size() +
                ", screenshots=" + screenshots.size() +
                '}';
    }
    
    /**
     * Generate a summary report
     */
    public String generateSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("Test: ").append(testName).append("\n");
        summary.append("Status: ").append(status).append("\n");
        summary.append("Duration: ").append(getDuration()).append("ms\n");
        summary.append("Steps executed: ").append(stepResults.size()).append("\n");
        
        if (!errors.isEmpty()) {
            summary.append("Errors:\n");
            for (String error : errors) {
                summary.append("  - ").append(error).append("\n");
            }
        }
        
        if (!screenshots.isEmpty()) {
            summary.append("Screenshots captured: ").append(screenshots.size()).append("\n");
        }
        
        return summary.toString();
    }
    
    /**
     * Test execution status
     */
    public enum Status {
        RUNNING,
        PASSED,
        FAILED,
        SKIPPED
    }
}
