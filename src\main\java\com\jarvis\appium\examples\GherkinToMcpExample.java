package com.jarvis.appium.examples;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Given-When-Then to MCP automation converter
 * Input your test scenarios in Gherkin format and let MCP execute them automatically
 */
public class GherkinToMcpExample {
    
    private static final Logger logger = LoggerFactory.getLogger(GherkinToMcpExample.class);
    
    // SauceLabs Sample App Configuration
    private static final String APK_PATH = "C:\\POC\\AI\\MCP Integration Server\\mcp-client-integration\\Android.SauceLabs.Mobile.Sample.app.2.7.1.apk";
    private static final String PACKAGE_NAME = "com.saucelabs.mydemoapp.android";
    private static final String ACTIVITY_NAME = ".view.activities.SplashActivity";
    
    private static BufferedWriter writer;
    private static int requestId = 1;
    private static String lastElementUUID = null;
    
    public static void main(String[] args) {
        try {
            // Define your test scenarios here in Given-When-Then format
            List<String> testScenarios = defineTestScenarios();
            
            // Execute scenarios with MCP
            executeGherkinScenarios(testScenarios);
            
        } catch (Exception e) {
            logger.error("Gherkin to MCP execution failed", e);
        }
    }
    
    /**
     * Define your test scenarios here in Given-When-Then format
     * Add your own scenarios to this list
     */
    private static List<String> defineTestScenarios() {
        List<String> scenarios = new ArrayList<>();
        
        // Scenario 1: Product Catalog Viewing
        scenarios.add("Scenario: View Product Catalog");
        scenarios.add("Given the SauceLabs app is launched");
        scenarios.add("When I view the product catalog");
        scenarios.add("Then I should see Sauce Labs Backpack");
        scenarios.add("And I should see Sauce Labs Bike Light");
        scenarios.add("And I should see the sort button");
        scenarios.add("");
        
        // Scenario 2: Add Product to Cart
        scenarios.add("Scenario: Add Product to Cart");
        scenarios.add("Given the SauceLabs app is launched");
        scenarios.add("When I click on Sauce Labs Backpack");
        scenarios.add("And I click the Add To Cart button");
        scenarios.add("Then the cart badge should show 1 item");
        scenarios.add("");
        
        // Scenario 3: Login Flow
        scenarios.add("Scenario: User Login");
        scenarios.add("Given the SauceLabs app is launched");
        scenarios.add("When I open the menu");
        scenarios.add("And I navigate to login");
        scenarios.add("And I enter username 'standard_user'");
        scenarios.add("And I enter password 'secret_sauce'");
        scenarios.add("And I click the login button");
        scenarios.add("Then I should be logged in successfully");
        scenarios.add("");
        
        // Scenario 4: Sort Products
        scenarios.add("Scenario: Sort Products by Name");
        scenarios.add("Given the SauceLabs app is launched");
        scenarios.add("When I click the sort button");
        scenarios.add("And I select sort by name A to Z");
        scenarios.add("Then products should be sorted alphabetically");
        scenarios.add("");
        
        // Add your own scenarios here!
        // scenarios.add("Scenario: Your Custom Test");
        // scenarios.add("Given your precondition");
        // scenarios.add("When your action");
        // scenarios.add("Then your expected result");
        
        return scenarios;
    }
    
    private static void executeGherkinScenarios(List<String> scenarios) throws IOException, InterruptedException {
        logger.info("=== Executing Given-When-Then Scenarios with MCP ===");
        
        // Start MCP server
        String[] command = {"node", "C:\\POC\\AI\\MCP Integration Server\\jarvis-appium\\dist\\index.js"};
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.redirectErrorStream(false);
        Process process = processBuilder.start();
        
        writer = new BufferedWriter(new OutputStreamWriter(process.getOutputStream()));
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        
        // Start output readers
        startOutputReaders(reader, errorReader);
        Thread.sleep(3000);
        
        // Initialize MCP
        initializeMcp();
        
        // Process each scenario
        String currentScenario = "";
        for (String line : scenarios) {
            line = line.trim();
            
            if (line.startsWith("Scenario:")) {
                currentScenario = line;
                logger.info("🎬 Starting: {}", currentScenario);
                continue;
            }
            
            if (line.isEmpty()) {
                logger.info("✅ Completed: {}", currentScenario);
                logger.info("");
                continue;
            }
            
            // Execute the step
            executeGherkinStep(line);
        }
        
        // Clean up
        cleanup(process, reader, errorReader);
        logger.info("=== All Scenarios Completed ===");
    }
    
    private static void initializeMcp() throws IOException, InterruptedException {
        logger.info("🔗 Initializing MCP connection...");
        sendMcpRequest("initialize", "{\"protocolVersion\":\"2024-11-05\",\"clientInfo\":{\"name\":\"gherkin-mcp-client\",\"version\":\"1.0.0\"},\"capabilities\":{\"roots\":{\"listChanged\":true},\"tools\":{\"listChanged\":true}}}");
        Thread.sleep(2000);
        
        logger.info("📱 Selecting Android platform...");
        sendMcpRequest("tools/call", "{\"name\":\"select_platform\",\"arguments\":{\"platform\":\"android\"}}");
        Thread.sleep(2000);
        
        logger.info("🚀 Creating session with SauceLabs app...");
        String sessionCapabilities = String.format(
            "{\"platform\":\"android\",\"capabilities\":{" +
            "\"platformName\":\"Android\"," +
            "\"app\":\"" + APK_PATH.replace("\\", "\\\\") + "\"," +
            "\"appPackage\":\"" + PACKAGE_NAME + "\"," +
            "\"appActivity\":\"" + ACTIVITY_NAME + "\"," +
            "\"automationName\":\"UiAutomator2\"," +
            "\"newCommandTimeout\":300," +
            "\"autoGrantPermissions\":true" +
            "}}"
        );
        sendMcpRequest("tools/call", "{\"name\":\"create_session\",\"arguments\":" + sessionCapabilities + "}");
        Thread.sleep(8000); // Wait for app to launch
    }
    
    private static void executeGherkinStep(String step) throws IOException, InterruptedException {
        logger.info("🔄 Executing: {}", step);
        
        // Parse and execute different types of steps
        if (step.matches(".*app is launched.*")) {
            executeAppLaunch();
        } else if (step.matches(".*view the product catalog.*")) {
            executeViewCatalog();
        } else if (step.matches(".*should see (.+)")) {
            executeVerifyElement(extractText(step, "should see (.+)"));
        } else if (step.matches(".*click on (.+)")) {
            executeClickElement(extractText(step, "click on (.+)"));
        } else if (step.matches(".*click the (.+)")) {
            executeClickElement(extractText(step, "click the (.+)"));
        } else if (step.matches(".*open the menu.*")) {
            executeOpenMenu();
        } else if (step.matches(".*navigate to login.*")) {
            executeNavigateToLogin();
        } else if (step.matches(".*enter username '(.+)'")) {
            executeEnterUsername(extractText(step, "enter username '(.+)'"));
        } else if (step.matches(".*enter password '(.+)'")) {
            executeEnterPassword(extractText(step, "enter password '(.+)'"));
        } else if (step.matches(".*cart badge should show (.+)")) {
            executeVerifyCartBadge(extractText(step, "cart badge should show (.+)"));
        } else if (step.matches(".*should be logged in successfully.*")) {
            executeVerifyLogin();
        } else if (step.matches(".*products should be sorted.*")) {
            executeVerifySort();
        } else {
            logger.warn("⚠️ Unknown step pattern: {}", step);
            // Take screenshot for unknown steps
            sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
            Thread.sleep(2000);
        }
    }
    
    // Step execution methods
    private static void executeAppLaunch() throws IOException, InterruptedException {
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(2000);
    }
    
    private static void executeViewCatalog() throws IOException, InterruptedException {
        sendMcpRequest("tools/call", "{\"name\":\"generate_locators\",\"arguments\":{}}");
        Thread.sleep(3000);
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(2000);
    }
    
    private static void executeVerifyElement(String elementText) throws IOException, InterruptedException {
        String selector = mapTextToSelector(elementText);
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"" + selector + "\"}}");
        Thread.sleep(2000);
    }
    
    private static void executeClickElement(String elementText) throws IOException, InterruptedException {
        String selector = mapTextToSelector(elementText);
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"" + selector + "\"}}");
        Thread.sleep(2000);
        // Note: In real implementation, you'd capture the UUID and click it
        // sendMcpRequest("tools/call", "{\"name\":\"appium_click\",\"arguments\":{\"elementUUID\":\"" + lastElementUUID + "\"}}");
    }
    
    private static void executeOpenMenu() throws IOException, InterruptedException {
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"accessibility id\",\"selector\":\"open menu\"}}");
        Thread.sleep(2000);
    }
    
    private static void executeNavigateToLogin() throws IOException, InterruptedException {
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@text='Login']\"}}");
        Thread.sleep(2000);
    }
    
    private static void executeEnterUsername(String username) throws IOException, InterruptedException {
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"accessibility id\",\"selector\":\"Username input field\"}}");
        Thread.sleep(2000);
        // Note: In real implementation, you'd use the UUID to set value
        // sendMcpRequest("tools/call", "{\"name\":\"appium_set_value\",\"arguments\":{\"elementUUID\":\"" + lastElementUUID + "\",\"text\":\"" + username + "\"}}");
    }
    
    private static void executeEnterPassword(String password) throws IOException, InterruptedException {
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"accessibility id\",\"selector\":\"Password input field\"}}");
        Thread.sleep(2000);
        // Note: In real implementation, you'd use the UUID to set value
        // sendMcpRequest("tools/call", "{\"name\":\"appium_set_value\",\"arguments\":{\"elementUUID\":\"" + lastElementUUID + "\",\"text\":\"" + password + "\"}}");
    }
    
    private static void executeVerifyCartBadge(String expectedCount) throws IOException, InterruptedException {
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@content-desc='cart badge']\"}}");
        Thread.sleep(2000);
    }
    
    private static void executeVerifyLogin() throws IOException, InterruptedException {
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(2000);
    }
    
    private static void executeVerifySort() throws IOException, InterruptedException {
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(2000);
    }
    
    // Helper methods
    private static String extractText(String step, String pattern) {
        Pattern p = Pattern.compile(pattern);
        Matcher m = p.matcher(step);
        return m.find() ? m.group(1) : "";
    }
    
    private static String mapTextToSelector(String text) {
        // Map common text to actual selectors
        switch (text.toLowerCase()) {
            case "sauce labs backpack":
                return "//*[@content-desc='Sauce Labs Backpack']";
            case "sauce labs bike light":
                return "//*[@content-desc='Sauce Labs Bike Light']";
            case "sort button":
                return "//*[@content-desc='sort button']";
            case "add to cart button":
                return "//*[@content-desc='Add To Cart button']";
            case "login button":
                return "//*[@content-desc='Login button']";
            default:
                return "//*[@text='" + text + "']";
        }
    }
    
    private static void sendMcpRequest(String method, String params) throws IOException {
        String request = "{\"jsonrpc\":\"2.0\",\"id\":\"" + (requestId++) + "\",\"method\":\"" + method + "\",\"params\":" + params + "}";
        logger.debug("Sending: {}", request);
        writer.write(request);
        writer.newLine();
        writer.flush();
    }
    
    private static void startOutputReaders(BufferedReader reader, BufferedReader errorReader) {
        Thread errorThread = new Thread(() -> {
            try {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    logger.debug("STDERR: {}", line);
                }
            } catch (IOException e) {
                logger.debug("Error reading stderr: {}", e.getMessage());
            }
        });
        errorThread.setDaemon(true);
        errorThread.start();
        
        Thread readerThread = new Thread(() -> {
            try {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (line.trim().startsWith("{")) {
                        logger.info("📨 MCP Response: {}", line);
                        // Parse response to extract element UUIDs for future use
                        // This is where you'd extract lastElementUUID from responses
                    } else {
                        logger.debug("Log: {}", line);
                    }
                }
            } catch (IOException e) {
                logger.debug("Error reading stdout: {}", e.getMessage());
            }
        });
        readerThread.setDaemon(true);
        readerThread.start();
    }
    
    private static void cleanup(Process process, BufferedReader reader, BufferedReader errorReader) throws IOException, InterruptedException {
        writer.close();
        reader.close();
        errorReader.close();
        process.destroy();
        if (!process.waitFor(5, TimeUnit.SECONDS)) {
            process.destroyForcibly();
        }
    }
}
