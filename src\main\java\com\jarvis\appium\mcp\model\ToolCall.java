package com.jarvis.appium.mcp.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;

/**
 * Represents a tool call request in MCP
 */
public class ToolCall {
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("arguments")
    private JsonNode arguments;
    
    public ToolCall() {}
    
    public ToolCall(String name, JsonNode arguments) {
        this.name = name;
        this.arguments = arguments;
    }
    
    // Getters and setters
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public JsonNode getArguments() {
        return arguments;
    }
    
    public void setArguments(JsonNode arguments) {
        this.arguments = arguments;
    }
    
    @Override
    public String toString() {
        return "ToolCall{" +
                "name='" + name + '\'' +
                ", arguments=" + arguments +
                '}';
    }
}
