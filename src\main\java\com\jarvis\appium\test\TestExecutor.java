package com.jarvis.appium.test;

import com.jarvis.appium.client.JarvisAppiumClient;
import com.jarvis.appium.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.List;

/**
 * Executes test scenarios using the Jarvis Appium client
 */
public class TestExecutor {
    
    private static final Logger logger = LoggerFactory.getLogger(TestExecutor.class);
    
    private final JarvisAppiumClient client;
    
    public TestExecutor(JarvisAppiumClient client) {
        this.client = client;
    }
    
    /**
     * Execute a test scenario
     */
    public TestResult executeScenario(TestScenario scenario) throws IOException {
        logger.info("Executing test scenario: {}", scenario.getName());
        
        TestResult result = new TestResult(scenario.getName());
        result.setStartTime(System.currentTimeMillis());
        
        try {
            // Ensure we have a session for the platform
            if (!client.isConnected()) {
                throw new IOException("Client is not connected to MCP server");
            }
            
            if (client.getCurrentPlatform() != scenario.getPlatform()) {
                logger.info("Switching to platform: {}", scenario.getPlatform());
                client.selectPlatform(scenario.getPlatform());
            }
            
            // Execute each step
            for (TestStep step : scenario.getSteps()) {
                try {
                    executeStep(step, result);
                } catch (Exception e) {
                    logger.error("Failed to execute step: {}", step.getDescription(), e);
                    result.addError(step.getDescription(), e.getMessage());
                    
                    // Take screenshot on failure
                    try {
                        String screenshot = client.takeScreenshot();
                        result.addScreenshot("failure_step_" + step.getOrder(), screenshot);
                    } catch (Exception screenshotError) {
                        logger.warn("Failed to take failure screenshot", screenshotError);
                    }
                    
                    // Continue with next step or fail fast based on configuration
                    // For now, we'll continue
                }
            }
            
            result.setStatus(result.getErrors().isEmpty() ? TestResult.Status.PASSED : TestResult.Status.FAILED);
            
        } catch (Exception e) {
            logger.error("Test scenario execution failed", e);
            result.setStatus(TestResult.Status.FAILED);
            result.addError("Scenario execution", e.getMessage());
        } finally {
            result.setEndTime(System.currentTimeMillis());
        }
        
        logger.info("Test scenario completed: {} - Status: {}", scenario.getName(), result.getStatus());
        return result;
    }
    
    /**
     * Execute a single test step
     */
    private void executeStep(TestStep step, TestResult result) throws IOException {
        logger.debug("Executing step: {}", step.getDescription());
        
        switch (step.getAction()) {
            case CLICK:
                executeClickStep(step);
                break;
            case SET_TEXT:
                executeSetTextStep(step);
                break;
            case GET_TEXT:
                executeGetTextStep(step, result);
                break;
            case VERIFY_TEXT:
                executeVerifyTextStep(step);
                break;
            case VERIFY_ELEMENT_PRESENT:
                executeVerifyElementPresentStep(step);
                break;
            case SCREENSHOT:
                executeScreenshotStep(step, result);
                break;
            case WAIT:
                executeWaitStep(step);
                break;
            case CUSTOM:
                executeCustomStep(step);
                break;
            default:
                logger.warn("Unsupported action: {}", step.getAction());
        }
        
        result.addStepResult(step.getDescription(), "SUCCESS");
    }
    
    private void executeClickStep(TestStep step) throws IOException {
        String elementId = findElementByDescription(step.getTarget());
        client.clickElement(elementId);
    }
    
    private void executeSetTextStep(TestStep step) throws IOException {
        String elementId = findElementByDescription(step.getTarget());
        client.setElementValue(elementId, step.getValue());
    }
    
    private void executeGetTextStep(TestStep step, TestResult result) throws IOException {
        String elementId = findElementByDescription(step.getTarget());
        String text = client.getElementText(elementId);
        result.addData(step.getDescription() + "_text", text);
    }
    
    private void executeVerifyTextStep(TestStep step) throws IOException {
        String elementId = findElementByDescription(step.getTarget());
        String actualText = client.getElementText(elementId);
        String expectedText = step.getValue();
        
        if (!expectedText.equals(actualText)) {
            throw new IOException("Text verification failed. Expected: '" + expectedText + 
                                "', Actual: '" + actualText + "'");
        }
    }
    
    private void executeVerifyElementPresentStep(TestStep step) throws IOException {
        try {
            findElementByDescription(step.getTarget());
            // If we get here, element was found
        } catch (IOException e) {
            throw new IOException("Element verification failed: " + step.getTarget());
        }
    }
    
    private void executeScreenshotStep(TestStep step, TestResult result) throws IOException {
        String screenshot = client.takeScreenshot();
        result.addScreenshot(step.getDescription(), screenshot);
    }
    
    private void executeWaitStep(TestStep step) {
        try {
            int waitTime = step.getValue() != null ? Integer.parseInt(step.getValue()) : 1000;
            Thread.sleep(waitTime);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } catch (NumberFormatException e) {
            logger.warn("Invalid wait time: {}, using default 1000ms", step.getValue());
            try {
                Thread.sleep(1000);
            } catch (InterruptedException ie) {
                Thread.currentThread().interrupt();
            }
        }
    }
    
    private void executeCustomStep(TestStep step) throws IOException {
        // For custom steps, we can use the target as a custom command
        // This is extensible for future custom actions
        logger.info("Executing custom step: {}", step.getDescription());
    }
    
    /**
     * Find element by description using intelligent locator generation
     */
    private String findElementByDescription(String description) throws IOException {
        // First, generate locators for current screen
        List<ElementLocator> locators = client.generateLocators();
        
        // Try to find element by text, content description, or other attributes
        for (ElementLocator locator : locators) {
            if (matchesDescription(locator, description)) {
                ElementLocator.LocatorInfo bestLocator = locator.getBestLocator(client.getCurrentPlatform());
                if (bestLocator != null) {
                    return client.findElement(bestLocator.getStrategy(), bestLocator.getValue());
                }
            }
        }
        
        throw new IOException("Could not find element matching description: " + description);
    }
    
    /**
     * Check if an element locator matches the given description
     */
    private boolean matchesDescription(ElementLocator locator, String description) {
        if (description == null) return false;
        
        String lowerDescription = description.toLowerCase();
        
        // Check text content
        if (locator.getText() != null && 
            locator.getText().toLowerCase().contains(lowerDescription)) {
            return true;
        }
        
        // Check content description
        if (locator.getContentDescription() != null && 
            locator.getContentDescription().toLowerCase().contains(lowerDescription)) {
            return true;
        }
        
        // Check if description matches any locator value
        if (locator.getLocators() != null) {
            for (String locatorValue : locator.getLocators().values()) {
                if (locatorValue != null && locatorValue.toLowerCase().contains(lowerDescription)) {
                    return true;
                }
            }
        }
        
        return false;
    }
}
