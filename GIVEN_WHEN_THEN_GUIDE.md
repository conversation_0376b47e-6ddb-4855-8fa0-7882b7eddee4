# 🎯 Given-When-Then to MCP Automation Guide

## ✨ What This Does

You write your test scenarios in **plain English** using Given-When-Then format, and the MCP server automatically executes them on your SauceLabs app!

## 🚀 Quick Start

### 1. Edit Your Test Scenarios
Open `test-scenarios.txt` and add your scenarios:

```gherkin
Scenario: Add Product to Cart
Given the SauceLabs app is launched
When I click on Sauce Labs Backpack
And I click the Add To Cart button
Then the cart badge should show 1 item
```

### 2. Run the Automation
```bash
mvn exec:java
```

### 3. Watch It Execute!
The automation will:
- 🔗 Connect to MCP server
- 📱 Launch your SauceLabs app
- 🎬 Execute each scenario step-by-step
- 📸 Take screenshots at key points
- ✅ Verify expected results

## 📝 Writing Test Scenarios

### Basic Format
```gherkin
Scenario: Your Test Name
Given [starting condition]
When [action you take]
And [additional actions]
Then [expected result]
And [additional verifications]
```

### Supported Step Patterns

#### 🎬 **Given Steps** (Setup)
- `Given the SauceLabs app is launched`
- `Given I have added [product] to cart`

#### 🎯 **When Steps** (Actions)
- `When I view the product catalog`
- `When I click on [element name]`
- `When I click the [button name]`
- `When I open the menu`
- `When I navigate to login`
- `When I enter username '[username]'`
- `When I enter password '[password]'`
- `When I select sort by [option]`

#### ✅ **Then Steps** (Verifications)
- `Then I should see [element name]`
- `Then the cart badge should show [number] item`
- `Then I should be logged in successfully`
- `Then products should be sorted alphabetically`

### 🛍️ SauceLabs App Elements You Can Use

#### Products
- `Sauce Labs Backpack`
- `Sauce Labs Bike Light`
- `Sauce Labs Bolt T-Shirt`
- `Sauce Labs Fleece Jacket`

#### Buttons & Controls
- `sort button`
- `Add To Cart button`
- `login button`
- `cart badge`

#### Login Credentials
- Username: `standard_user`
- Password: `secret_sauce`

## 📋 Example Scenarios

### 🛒 E-commerce Testing
```gherkin
Scenario: Complete Shopping Flow
Given the SauceLabs app is launched
When I click on Sauce Labs Backpack
And I click the Add To Cart button
And I click on cart badge
Then I should see the product in cart
And the total should be correct

Scenario: Multiple Product Selection
Given the SauceLabs app is launched
When I click on Sauce Labs Backpack
And I click the Add To Cart button
And I click on Sauce Labs Bike Light
And I click the Add To Cart button
Then the cart badge should show 2 items
```

### 🔐 Authentication Testing
```gherkin
Scenario: Valid Login
Given the SauceLabs app is launched
When I open the menu
And I navigate to login
And I enter username 'standard_user'
And I enter password 'secret_sauce'
And I click the login button
Then I should be logged in successfully

Scenario: Invalid Login
Given the SauceLabs app is launched
When I open the menu
And I navigate to login
And I enter username 'invalid_user'
And I enter password 'wrong_password'
And I click the login button
Then I should see an error message
```

### 📊 UI Testing
```gherkin
Scenario: Sort Products
Given the SauceLabs app is launched
When I click the sort button
And I select sort by name A to Z
Then products should be sorted alphabetically

Scenario: Product Details
Given the SauceLabs app is launched
When I click on Sauce Labs Backpack
Then I should see product details
And I should see the product price
And I should see the Add To Cart button
```

## 🔧 How It Works

### 1. **Scenario Parsing**
- Reads `test-scenarios.txt`
- Parses Given-When-Then steps
- Maps steps to MCP actions

### 2. **MCP Integration**
- Connects to Jarvis Appium MCP server
- Translates steps to MCP tool calls
- Executes actions on real device/emulator

### 3. **Element Mapping**
- Maps friendly names to actual selectors
- Uses accessibility IDs and XPath
- Handles SauceLabs app specific elements

### 4. **Execution Flow**
```
Your Scenario → Step Parser → MCP Tool Call → Device Action → Result Verification
```

## 🎯 Advanced Features

### Custom Element Mapping
Add your own element mappings in `mapTextToSelector()`:

```java
case "my custom button":
    return "//*[@content-desc='my-button-id']";
```

### New Step Patterns
Add new step patterns in `executeGherkinStep()`:

```java
else if (step.matches(".*my custom action (.+)")) {
    executeMyCustomAction(extractText(step, "my custom action (.+)"));
}
```

### Screenshot Points
Screenshots are automatically taken at:
- App launch
- After each verification step
- When unknown steps are encountered
- At scenario completion

## 🚨 Prerequisites

1. **Android device/emulator connected**
   ```bash
   adb devices
   ```

2. **SauceLabs APK available** at configured path

3. **MCP server accessible** at the specified location

## 🎉 Benefits

### ✅ **For Manual Testers**
- Write tests in plain English
- No coding required
- Focus on test logic, not implementation

### ✅ **For Automation Engineers**
- Rapid test creation
- Maintainable test scenarios
- Easy to extend and customize

### ✅ **For Teams**
- Shared understanding of test cases
- Living documentation
- Collaborative test development

## 🔄 Workflow

1. **📝 Write** scenarios in `test-scenarios.txt`
2. **🚀 Run** `mvn exec:java`
3. **👀 Watch** automation execute your scenarios
4. **📸 Review** screenshots and results
5. **🔄 Iterate** and add more scenarios

## 🎯 Next Steps

1. **Start Simple**: Begin with basic scenarios
2. **Add Complexity**: Gradually add more complex workflows
3. **Customize**: Extend with your own step patterns
4. **Scale**: Build comprehensive test suites

Your Given-When-Then automation is ready! 🚀

Just edit `test-scenarios.txt` and run `mvn exec:java` to see your scenarios come to life!
