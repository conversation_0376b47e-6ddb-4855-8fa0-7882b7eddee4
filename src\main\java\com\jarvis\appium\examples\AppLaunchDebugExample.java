package com.jarvis.appium.examples;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.concurrent.TimeUnit;

/**
 * Debug tool to verify app launch and see what's actually on screen
 */
public class AppLaunchDebugExample {
    
    private static final Logger logger = LoggerFactory.getLogger(AppLaunchDebugExample.class);
    
    private static final String APK_PATH = "C:\\POC\\AI\\MCP Integration Server\\mcp-client-integration\\Android.SauceLabs.Mobile.Sample.app.2.7.1.apk";
    private static final String PACKAGE_NAME = "com.saucelabs.mydemoapp.android";
    private static final String ACTIVITY_NAME = ".view.activities.SplashActivity";
    
    private static BufferedWriter writer;
    private static int requestId = 1;
    
    public static void main(String[] args) {
        try {
            debugAppLaunch();
        } catch (Exception e) {
            logger.error("App launch debug failed", e);
        }
    }
    
    private static void debugAppLaunch() throws IOException, InterruptedException {
        logger.info("=== 🚀 App Launch Debug Tool ===");
        
        // Start MCP server
        String[] command = {"node", "dist/index.js"};
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.directory(new File("C:\\POC\\AI\\MCP Integration Server\\jarvis-appium"));
        processBuilder.redirectErrorStream(false);
        Process process = processBuilder.start();
        
        writer = new BufferedWriter(new OutputStreamWriter(process.getOutputStream()));
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        
        // Start output readers
        startOutputReaders(reader, errorReader);
        Thread.sleep(3000);
        
        // Initialize MCP
        logger.info("🔗 Initializing MCP connection...");
        sendMcpRequest("initialize", "{\"protocolVersion\":\"2024-11-05\",\"clientInfo\":{\"name\":\"app-launch-debug\",\"version\":\"1.0.0\"},\"capabilities\":{\"roots\":{\"listChanged\":true},\"tools\":{\"listChanged\":true}}}");
        Thread.sleep(3000);
        
        logger.info("📱 Selecting Android platform...");
        sendMcpRequest("tools/call", "{\"name\":\"select_platform\",\"arguments\":{\"platform\":\"android\"}}");
        Thread.sleep(3000);
        
        // Check if APK file exists
        File apkFile = new File(APK_PATH);
        logger.info("📦 APK File Check:");
        logger.info("   Path: {}", APK_PATH);
        logger.info("   Exists: {}", apkFile.exists());
        logger.info("   Size: {} bytes", apkFile.exists() ? apkFile.length() : "N/A");
        
        // Try multiple session creation approaches
        logger.info("🚀 Attempt 1: Creating session with working example format...");
        String sessionCapabilities1 = String.format(
            "{\"platform\":\"android\",\"capabilities\":{" +
            "\"platformName\":\"Android\"," +
            "\"app\":\"" + APK_PATH.replace("\\", "\\\\") + "\"," +
            "\"appPackage\":\"" + PACKAGE_NAME + "\"," +
            "\"appActivity\":\"" + ACTIVITY_NAME + "\"," +
            "\"automationName\":\"UiAutomator2\"," +
            "\"newCommandTimeout\":300," +
            "\"autoGrantPermissions\":true" +
            "}}"
        );

        logger.info("📋 Session capabilities 1: {}", sessionCapabilities1);
        sendMcpRequest("tools/call", "{\"name\":\"create_session\",\"arguments\":" + sessionCapabilities1 + "}");

        logger.info("⏳ Waiting for app to launch (15 seconds)...");
        Thread.sleep(15000);

        // Check if app launched
        logger.info("🔍 Checking if SauceLabs app launched...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@package='" + PACKAGE_NAME + "']\"}}");
        Thread.sleep(3000);

        // If first attempt failed, try alternative format
        logger.info("🚀 Attempt 2: Creating session with alternative format...");
        String sessionCapabilities2 = String.format(
            "{\"platform\":\"android\",\"capabilities\":{" +
            "\"platformName\":\"Android\"," +
            "\"deviceName\":\"emulator-5554\"," +
            "\"app\":\"" + APK_PATH.replace("\\", "\\\\") + "\"," +
            "\"appPackage\":\"" + PACKAGE_NAME + "\"," +
            "\"appActivity\":\"" + ACTIVITY_NAME + "\"," +
            "\"automationName\":\"UiAutomator2\"," +
            "\"newCommandTimeout\":300," +
            "\"autoGrantPermissions\":true," +
            "\"noReset\":true," +
            "\"fullReset\":false" +
            "}}"
        );

        logger.info("📋 Session capabilities 2: {}", sessionCapabilities2);
        sendMcpRequest("tools/call", "{\"name\":\"create_session\",\"arguments\":" + sessionCapabilities2 + "}");

        logger.info("⏳ Waiting for app to launch (15 seconds)...");
        Thread.sleep(15000);
        
        // Take screenshot to see what's on screen
        logger.info("📸 Taking screenshot to see current screen...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        // Get current activity
        logger.info("🔍 Getting current activity...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*\"}}");
        Thread.sleep(3000);
        
        // Try to find any element with SauceLabs in it
        logger.info("🔍 Looking for any SauceLabs elements...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[contains(@text,'Sauce')]\"}}");
        Thread.sleep(3000);
        
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[contains(@content-desc,'Sauce')]\"}}");
        Thread.sleep(3000);
        
        // Try to find common Android elements
        logger.info("🔍 Looking for common Android elements...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"id\",\"selector\":\"android:id/content\"}}");
        Thread.sleep(3000);
        
        // Generate locators to see all available elements
        logger.info("📋 Generating locators for current screen...");
        sendMcpRequest("tools/call", "{\"name\":\"generate_locators\",\"arguments\":{}}");
        Thread.sleep(5000);
        
        // Try alternative launch method - launch by package/activity directly
        logger.info("🔄 Trying alternative launch method...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@package='" + PACKAGE_NAME + "']\"}}");
        Thread.sleep(3000);

        // Try direct app launch using MCP tools
        logger.info("🚀 Attempt 3: Direct app launch using MCP tools...");
        String launchParams = String.format(
            "{\"packageName\":\"%s\",\"activityName\":\"%s\"}",
            PACKAGE_NAME, ACTIVITY_NAME
        );
        sendMcpRequest("tools/call", "{\"name\":\"launch_app\",\"arguments\":" + launchParams + "}");
        Thread.sleep(5000);

        // Check if app launched after direct launch
        logger.info("🔍 Checking if app launched after direct launch...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@package='" + PACKAGE_NAME + "']\"}}");
        Thread.sleep(3000);

        // Try installing the app first, then launching
        logger.info("🚀 Attempt 4: Install app first, then launch...");
        String installParams = String.format(
            "{\"appPath\":\"%s\"}",
            APK_PATH.replace("\\", "\\\\")
        );
        sendMcpRequest("tools/call", "{\"name\":\"install_app\",\"arguments\":" + installParams + "}");
        Thread.sleep(10000);

        // Now try launching after install
        sendMcpRequest("tools/call", "{\"name\":\"launch_app\",\"arguments\":" + launchParams + "}");
        Thread.sleep(5000);

        // Final check
        logger.info("🔍 Final check for SauceLabs app...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@package='" + PACKAGE_NAME + "']\"}}");
        Thread.sleep(3000);

        // Clean up
        logger.info("🧹 Cleaning up...");
        writer.close();
        reader.close();
        errorReader.close();
        
        process.destroy();
        if (!process.waitFor(5, TimeUnit.SECONDS)) {
            process.destroyForcibly();
        }
        
        logger.info("=== App Launch Debug Complete ===");
        logger.info("💡 Check the logs above to see:");
        logger.info("   1. Whether the APK file exists and is accessible");
        logger.info("   2. What elements are actually on screen");
        logger.info("   3. Whether the SauceLabs app launched successfully");
    }
    
    private static void sendMcpRequest(String method, String params) throws IOException {
        String request = "{\"jsonrpc\":\"2.0\",\"id\":\"" + (requestId++) + "\",\"method\":\"" + method + "\",\"params\":" + params + "}";
        writer.write(request);
        writer.newLine();
        writer.flush();
    }
    
    private static void startOutputReaders(BufferedReader reader, BufferedReader errorReader) {
        Thread errorThread = new Thread(() -> {
            try {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    if (line.contains("ERROR") || line.contains("WARN")) {
                        logger.warn("🔴 MCP STDERR: {}", line);
                    } else if (line.contains("info") || line.contains("dbug")) {
                        logger.info("🔵 MCP STDERR: {}", line);
                    }
                }
            } catch (IOException e) {
                // Ignore
            }
        });
        errorThread.setDaemon(true);
        errorThread.start();
        
        Thread readerThread = new Thread(() -> {
            try {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (line.trim().startsWith("{")) {
                        if (line.contains("\"result\"") && line.contains("\"content\"")) {
                            // Extract meaningful content from MCP responses
                            if (line.contains("session created successfully")) {
                                logger.info("✅ Session created successfully!");
                            } else if (line.contains("Failed to find element")) {
                                logger.warn("❌ Element not found: {}", extractFailureReason(line));
                            } else if (line.contains("interactableElements")) {
                                logger.info("📋 Found interactable elements on screen");
                                // Parse and show first few elements
                                String elements = extractElements(line);
                                logger.info("🔍 Available elements: {}", elements);
                            } else {
                                logger.info("📨 MCP Response: {}", line.length() > 200 ? line.substring(0, 200) + "..." : line);
                            }
                        } else {
                            logger.debug("📨 MCP: {}", line.length() > 100 ? line.substring(0, 100) + "..." : line);
                        }
                    }
                }
            } catch (IOException e) {
                // Ignore
            }
        });
        readerThread.setDaemon(true);
        readerThread.start();
    }
    
    private static String extractFailureReason(String line) {
        try {
            if (line.contains("Failed to find element")) {
                int start = line.indexOf("Failed to find element");
                int end = line.indexOf("with strategy", start);
                if (end > start) {
                    return line.substring(start, end).trim();
                }
            }
            return "Unknown failure";
        } catch (Exception e) {
            return "Parse error";
        }
    }
    
    private static String extractElements(String line) {
        try {
            if (line.contains("interactableElements")) {
                // Extract first few element types
                int start = line.indexOf("interactableElements");
                String elements = line.substring(start, Math.min(start + 300, line.length()));
                return elements.replaceAll("\\\\", "").replaceAll("\"", "'");
            }
            return "No elements found";
        } catch (Exception e) {
            return "Parse error";
        }
    }
}
