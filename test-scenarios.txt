# 🎯 Your Given-When-Then Test Scenarios
# Edit this file to add your own test scenarios
# The automation will read and execute these scenarios

Scenario: View Product Catalog
Given the SauceLabs app is launched
When I login to the app with valid credentials "standard_user" and "secret_sauce"
And I view the product catalog
Then I should see Sauce Labs Backpack
And I should see Sauce Labs Bike Light
And I should see the sort button



# 📝 Add Your Own Scenarios Below:
# Copy the format above and customize for your needs

# Scenario: Your Custom Test Name
# Given your starting condition
# When your action steps
# And additional actions
# Then your expected results
# And additional verifications

# Example: Testing Product Details
# Scenario: View Product Details
# Given the SauceLabs app is launched
# When I click on Sauce Labs Backpack
# Then I should see product details
# And I should see the product price
# And I should see the Add To Cart button

# Example: Testing Cart Functionality
# Scenario: Remove Item from Cart
# Given I have added Sauce Labs Backpack to cart
# When I open the cart
# And I click remove item
# Then the cart should be empty
# And the cart badge should show 0 items

# Example: Testing Invalid Login
# Scenario: Invalid Login Attempt
# Given the SauceLabs app is launched
# When I open the menu
# And I navigate to login
# And I enter username 'invalid_user'
# And I enter password 'wrong_password'
# And I click the login button
# Then I should see an error message
# And I should remain on the login screen
