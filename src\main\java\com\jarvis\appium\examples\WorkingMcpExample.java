package com.jarvis.appium.examples;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.concurrent.TimeUnit;

/**
 * Working MCP client example with proper initialization
 */
public class WorkingMcpExample {
    
    private static final Logger logger = LoggerFactory.getLogger(WorkingMcpExample.class);
    
    public static void main(String[] args) {
        try {
            testWorkingMcpClient();
        } catch (Exception e) {
            logger.error("Working MCP test failed", e);
        }
    }
    
    private static void testWorkingMcpClient() throws IOException, InterruptedException {
        logger.info("=== Testing Working MCP Client ===");
        
        String[] command = {"node", "C:\\POC\\AI\\MCP Integration Server\\jarvis-appium\\dist\\index.js"};
        
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.redirectErrorStream(false);
        
        Process process = processBuilder.start();
        
        BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(process.getOutputStream()));
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        
        // Start thread to read stderr
        Thread errorThread = new Thread(() -> {
            try {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    logger.debug("STDERR: {}", line);
                }
            } catch (IOException e) {
                logger.debug("Error reading stderr: {}", e.getMessage());
            }
        });
        errorThread.setDaemon(true);
        errorThread.start();
        
        // Start thread to read stdout (skip log messages, only process JSON)
        Thread readerThread = new Thread(() -> {
            try {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (line.trim().startsWith("{")) {
                        logger.info("JSON Response: {}", line);
                    } else {
                        logger.debug("Log message: {}", line);
                    }
                }
            } catch (IOException e) {
                logger.debug("Error reading stdout: {}", e.getMessage());
            }
        });
        readerThread.setDaemon(true);
        readerThread.start();
        
        // Wait for server to start
        Thread.sleep(3000);
        
        // Send proper initialize request with capabilities
        String initRequest = "{\n" +
                "  \"jsonrpc\": \"2.0\",\n" +
                "  \"id\": \"init-1\",\n" +
                "  \"method\": \"initialize\",\n" +
                "  \"params\": {\n" +
                "    \"protocolVersion\": \"2024-11-05\",\n" +
                "    \"clientInfo\": {\n" +
                "      \"name\": \"jarvis-appium-java-client\",\n" +
                "      \"version\": \"1.0.0\"\n" +
                "    },\n" +
                "    \"capabilities\": {\n" +
                "      \"roots\": {\n" +
                "        \"listChanged\": true\n" +
                "      },\n" +
                "      \"tools\": {\n" +
                "        \"listChanged\": true\n" +
                "      }\n" +
                "    }\n" +
                "  }\n" +
                "}";
        
        logger.info("Sending initialize request...");
        writer.write(initRequest);
        writer.newLine();
        writer.flush();
        
        // Wait for initialization response
        Thread.sleep(2000);
        
        // Send a tool call to test functionality
        String toolCallRequest = "{\n" +
                "  \"jsonrpc\": \"2.0\",\n" +
                "  \"id\": \"tool-1\",\n" +
                "  \"method\": \"tools/call\",\n" +
                "  \"params\": {\n" +
                "    \"name\": \"select_platform\",\n" +
                "    \"arguments\": {\n" +
                "      \"platform\": \"android\"\n" +
                "    }\n" +
                "  }\n" +
                "}";
        
        logger.info("Sending tool call request...");
        writer.write(toolCallRequest);
        writer.newLine();
        writer.flush();
        
        // Wait for tool response
        Thread.sleep(2000);
        
        // Send tools/list request to see available tools
        String toolsListRequest = "{\n" +
                "  \"jsonrpc\": \"2.0\",\n" +
                "  \"id\": \"tools-list-1\",\n" +
                "  \"method\": \"tools/list\",\n" +
                "  \"params\": {}\n" +
                "}";
        
        logger.info("Sending tools/list request...");
        writer.write(toolsListRequest);
        writer.newLine();
        writer.flush();
        
        // Wait for final response
        Thread.sleep(3000);
        
        // Clean up
        writer.close();
        reader.close();
        errorReader.close();
        
        process.destroy();
        if (!process.waitFor(5, TimeUnit.SECONDS)) {
            process.destroyForcibly();
        }
        
        logger.info("=== Test Complete ===");
    }
}
