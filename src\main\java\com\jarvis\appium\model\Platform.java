package com.jarvis.appium.model;

/**
 * Represents mobile platforms supported by Jarvis Appium
 */
public enum Platform {
    ANDROID("android"),
    IOS("ios");
    
    private final String value;
    
    Platform(String value) {
        this.value = value;
    }
    
    public String getValue() {
        return value;
    }
    
    public static Platform fromString(String value) {
        for (Platform platform : Platform.values()) {
            if (platform.value.equalsIgnoreCase(value)) {
                return platform;
            }
        }
        throw new IllegalArgumentException("Unknown platform: " + value);
    }
    
    @Override
    public String toString() {
        return value;
    }
}
