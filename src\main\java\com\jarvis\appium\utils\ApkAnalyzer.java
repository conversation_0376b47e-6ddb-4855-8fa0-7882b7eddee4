package com.jarvis.appium.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Utility to analyze APK files and extract configuration information
 */
public class ApkAnalyzer {
    
    private static final Logger logger = LoggerFactory.getLogger(ApkAnalyzer.class);
    
    public static class ApkInfo {
        public String packageName;
        public String mainActivity;
        public String versionName;
        public String versionCode;
        public String minSdkVersion;
        public String targetSdkVersion;
        public String appName;
        
        @Override
        public String toString() {
            return String.format(
                "APK Information:\n" +
                "  Package Name: %s\n" +
                "  Main Activity: %s\n" +
                "  App Name: %s\n" +
                "  Version: %s (%s)\n" +
                "  Min SDK: %s\n" +
                "  Target SDK: %s",
                packageName, mainActivity, appName, versionName, versionCode, minSdkVersion, targetSdkVersion
            );
        }
        
        public String getJavaConfiguration() {
            return String.format(
                "// Configuration for your APK\n" +
                "private static final String APK_PATH = \"YOUR_APK_PATH_HERE\";\n" +
                "private static final String PACKAGE_NAME = \"%s\";\n" +
                "private static final String ACTIVITY_NAME = \"%s\";\n" +
                "private static final String APP_NAME = \"%s\";",
                packageName, 
                mainActivity.startsWith(packageName) ? mainActivity.substring(packageName.length()) : mainActivity,
                appName != null ? appName : "Unknown App"
            );
        }
    }
    
    public static void main(String[] args) {
        if (args.length == 0) {
            System.out.println("Usage: java ApkAnalyzer <path-to-apk>");
            System.out.println("Example: java ApkAnalyzer C:\\Downloads\\myapp.apk");
            return;
        }
        
        String apkPath = args[0];
        ApkInfo info = analyzeApk(apkPath);
        
        if (info != null) {
            System.out.println("========================================");
            System.out.println(info.toString());
            System.out.println("========================================");
            System.out.println("\nJava Configuration:");
            System.out.println(info.getJavaConfiguration());
            System.out.println("========================================");
        } else {
            System.out.println("Failed to analyze APK. Make sure aapt is in your PATH.");
        }
    }
    
    public static ApkInfo analyzeApk(String apkPath) {
        try {
            logger.info("Analyzing APK: {}", apkPath);
            
            // Run aapt command to get APK information
            ProcessBuilder pb = new ProcessBuilder("aapt", "dump", "badging", apkPath);
            Process process = pb.start();
            
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            StringBuilder output = new StringBuilder();
            String line;
            
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }
            
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                logger.error("aapt command failed with exit code: {}", exitCode);
                return null;
            }
            
            return parseAaptOutput(output.toString());
            
        } catch (IOException | InterruptedException e) {
            logger.error("Error analyzing APK", e);
            return null;
        }
    }
    
    private static ApkInfo parseAaptOutput(String output) {
        ApkInfo info = new ApkInfo();
        
        // Extract package name
        Pattern packagePattern = Pattern.compile("package: name='([^']+)'");
        Matcher packageMatcher = packagePattern.matcher(output);
        if (packageMatcher.find()) {
            info.packageName = packageMatcher.group(1);
        }
        
        // Extract version info
        Pattern versionPattern = Pattern.compile("versionName='([^']+)'");
        Matcher versionMatcher = versionPattern.matcher(output);
        if (versionMatcher.find()) {
            info.versionName = versionMatcher.group(1);
        }
        
        Pattern versionCodePattern = Pattern.compile("versionCode='([^']+)'");
        Matcher versionCodeMatcher = versionCodePattern.matcher(output);
        if (versionCodeMatcher.find()) {
            info.versionCode = versionCodeMatcher.group(1);
        }
        
        // Extract main activity
        Pattern activityPattern = Pattern.compile("launchable-activity: name='([^']+)'");
        Matcher activityMatcher = activityPattern.matcher(output);
        if (activityMatcher.find()) {
            info.mainActivity = activityMatcher.group(1);
        }
        
        // Extract app name
        Pattern appNamePattern = Pattern.compile("application-label:'([^']+)'");
        Matcher appNameMatcher = appNamePattern.matcher(output);
        if (appNameMatcher.find()) {
            info.appName = appNameMatcher.group(1);
        }
        
        // Extract SDK versions
        Pattern minSdkPattern = Pattern.compile("sdkVersion:'([^']+)'");
        Matcher minSdkMatcher = minSdkPattern.matcher(output);
        if (minSdkMatcher.find()) {
            info.minSdkVersion = minSdkMatcher.group(1);
        }
        
        Pattern targetSdkPattern = Pattern.compile("targetSdkVersion:'([^']+)'");
        Matcher targetSdkMatcher = targetSdkPattern.matcher(output);
        if (targetSdkMatcher.find()) {
            info.targetSdkVersion = targetSdkMatcher.group(1);
        }
        
        return info;
    }
}
