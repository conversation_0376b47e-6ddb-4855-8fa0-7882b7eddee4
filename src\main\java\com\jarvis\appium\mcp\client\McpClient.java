package com.jarvis.appium.mcp.client;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jarvis.appium.mcp.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * Core MCP client for communicating with the Jarvis Appium MCP server via stdio
 */
public class McpClient implements AutoCloseable {
    
    private static final Logger logger = LoggerFactory.getLogger(McpClient.class);
    private static final int DEFAULT_TIMEOUT_SECONDS = 30;
    
    private final ObjectMapper objectMapper;
    private final ConcurrentHashMap<String, CompletableFuture<McpResponse>> pendingRequests;
    private Process serverProcess;
    private BufferedWriter writer;
    private BufferedReader reader;
    private Thread readerThread;
    private volatile boolean isConnected = false;
    
    public McpClient() {
        this.objectMapper = new ObjectMapper();
        this.pendingRequests = new ConcurrentHashMap<>();
    }
    
    /**
     * Connect to the MCP server using stdio
     */
    public void connect(String... serverCommand) throws IOException {
        if (isConnected) {
            throw new IllegalStateException("Client is already connected");
        }
        
        logger.info("Starting MCP server with command: {}", String.join(" ", serverCommand));
        
        ProcessBuilder processBuilder = new ProcessBuilder(serverCommand);
        processBuilder.redirectErrorStream(false);
        
        serverProcess = processBuilder.start();
        
        writer = new BufferedWriter(new OutputStreamWriter(serverProcess.getOutputStream()));
        reader = new BufferedReader(new InputStreamReader(serverProcess.getInputStream()));
        
        // Start reader thread to handle responses
        readerThread = new Thread(this::readResponses);
        readerThread.setDaemon(true);
        readerThread.start();
        
        isConnected = true;
        logger.info("Connected to MCP server");
        
        // Initialize the connection
        initialize();
    }
    
    /**
     * Initialize the MCP connection
     */
    private void initialize() throws IOException {
        JsonNode initParams = objectMapper.createObjectNode()
                .put("protocolVersion", "2024-11-05")
                .put("clientInfo", objectMapper.createObjectNode()
                        .put("name", "jarvis-appium-java-client")
                        .put("version", "1.0.0"));
        
        McpResponse response = sendRequest("initialize", initParams);
        if (response.hasError()) {
            throw new IOException("Failed to initialize MCP connection: " + response.getError());
        }
        
        logger.info("MCP connection initialized successfully");
    }
    
    /**
     * Send a request to the MCP server
     */
    public McpResponse sendRequest(String method, JsonNode params) throws IOException {
        return sendRequest(method, params, DEFAULT_TIMEOUT_SECONDS);
    }
    
    /**
     * Send a request to the MCP server with timeout
     */
    public McpResponse sendRequest(String method, JsonNode params, int timeoutSeconds) throws IOException {
        if (!isConnected) {
            throw new IllegalStateException("Client is not connected");
        }
        
        String requestId = UUID.randomUUID().toString();
        McpRequest request = new McpRequest(requestId, method, params);
        
        CompletableFuture<McpResponse> future = new CompletableFuture<>();
        pendingRequests.put(requestId, future);
        
        try {
            String requestJson = objectMapper.writeValueAsString(request);
            logger.debug("Sending request: {}", requestJson);
            
            synchronized (writer) {
                writer.write(requestJson);
                writer.newLine();
                writer.flush();
            }
            
            return future.get(timeoutSeconds, TimeUnit.SECONDS);
            
        } catch (Exception e) {
            pendingRequests.remove(requestId);
            throw new IOException("Failed to send request: " + e.getMessage(), e);
        }
    }
    
    /**
     * Call a tool on the MCP server
     */
    public ToolResult callTool(String toolName, JsonNode arguments) throws IOException {
        JsonNode params = objectMapper.createObjectNode()
                .put("name", toolName)
                .set("arguments", arguments);
        
        McpResponse response = sendRequest("tools/call", params);
        
        if (response.hasError()) {
            throw new IOException("Tool call failed: " + response.getError().getMessage());
        }
        
        return objectMapper.treeToValue(response.getResult(), ToolResult.class);
    }
    
    /**
     * Read responses from the server
     */
    private void readResponses() {
        try {
            String line;
            while ((line = reader.readLine()) != null && isConnected) {
                logger.debug("Received response: {}", line);
                
                try {
                    McpResponse response = objectMapper.readValue(line, McpResponse.class);
                    CompletableFuture<McpResponse> future = pendingRequests.remove(response.getId());
                    
                    if (future != null) {
                        future.complete(response);
                    } else {
                        logger.warn("Received response for unknown request ID: {}", response.getId());
                    }
                    
                } catch (Exception e) {
                    logger.error("Failed to parse response: {}", line, e);
                }
            }
        } catch (IOException e) {
            if (isConnected) {
                logger.error("Error reading from server", e);
            }
        }
    }
    
    @Override
    public void close() throws IOException {
        if (!isConnected) {
            return;
        }
        
        isConnected = false;
        
        // Complete any pending requests with error
        pendingRequests.values().forEach(future -> 
            future.completeExceptionally(new IOException("Connection closed")));
        pendingRequests.clear();
        
        // Close streams
        if (writer != null) {
            writer.close();
        }
        if (reader != null) {
            reader.close();
        }
        
        // Stop reader thread
        if (readerThread != null) {
            readerThread.interrupt();
        }
        
        // Destroy server process
        if (serverProcess != null) {
            serverProcess.destroy();
            try {
                if (!serverProcess.waitFor(5, TimeUnit.SECONDS)) {
                    serverProcess.destroyForcibly();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                serverProcess.destroyForcibly();
            }
        }
        
        logger.info("MCP client disconnected");
    }
    
    public boolean isConnected() {
        return isConnected;
    }
}
