package com.jarvis.appium.examples;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.concurrent.TimeUnit;

/**
 * Element discovery tool to find available elements on the current screen
 */
public class ElementDiscoveryExample {
    
    private static final Logger logger = LoggerFactory.getLogger(ElementDiscoveryExample.class);
    
    private static final String APK_PATH = "C:\\POC\\AI\\MCP Integration Server\\mcp-client-integration\\Android.SauceLabs.Mobile.Sample.app.2.7.1.apk";
    private static final String PACKAGE_NAME = "com.saucelabs.mydemoapp.android";
    private static final String ACTIVITY_NAME = ".view.activities.SplashActivity";
    
    private static BufferedWriter writer;
    private static int requestId = 1;
    
    public static void main(String[] args) {
        try {
            discoverElements();
        } catch (Exception e) {
            logger.error("Element discovery failed", e);
        }
    }
    
    private static void discoverElements() throws IOException, InterruptedException {
        logger.info("=== 🔍 Element Discovery Tool ===");
        
        // Start MCP server
        String[] command = {"node", "dist/index.js"};
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.directory(new File("C:\\POC\\AI\\MCP Integration Server\\jarvis-appium"));
        processBuilder.redirectErrorStream(false);
        Process process = processBuilder.start();
        
        writer = new BufferedWriter(new OutputStreamWriter(process.getOutputStream()));
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        
        // Start output readers
        startOutputReaders(reader, errorReader);
        Thread.sleep(3000);
        
        // Initialize MCP
        logger.info("🔗 Initializing MCP connection...");
        sendMcpRequest("initialize", "{\"protocolVersion\":\"2024-11-05\",\"clientInfo\":{\"name\":\"element-discovery\",\"version\":\"1.0.0\"},\"capabilities\":{\"roots\":{\"listChanged\":true},\"tools\":{\"listChanged\":true}}}");
        Thread.sleep(3000);
        
        logger.info("📱 Selecting Android platform...");
        sendMcpRequest("tools/call", "{\"name\":\"select_platform\",\"arguments\":{\"platform\":\"android\"}}");
        Thread.sleep(3000);
        
        logger.info("🚀 Creating session with SauceLabs app...");
        String sessionCapabilities = String.format(
            "{\"platform\":\"android\",\"capabilities\":{" +
            "\"platformName\":\"Android\"," +
            "\"deviceName\":\"emulator-5554\"," +
            "\"app\":\"" + APK_PATH.replace("\\", "\\\\") + "\"," +
            "\"appPackage\":\"" + PACKAGE_NAME + "\"," +
            "\"appActivity\":\"" + ACTIVITY_NAME + "\"," +
            "\"automationName\":\"UiAutomator2\"," +
            "\"newCommandTimeout\":300," +
            "\"autoGrantPermissions\":true," +
            "\"noReset\":false," +
            "\"fullReset\":false" +
            "}}"
        );
        
        sendMcpRequest("tools/call", "{\"name\":\"create_session\",\"arguments\":" + sessionCapabilities + "}");
        Thread.sleep(15000); // Wait for app to launch
        
        // Take screenshot
        logger.info("📸 Taking screenshot...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        // Try to find common elements with different strategies
        logger.info("🔍 Discovering elements...");
        
        // Look for menu elements
        logger.info("Looking for menu elements...");
        tryFindElement("Menu (accessibility)", "accessibility id", "open menu");
        tryFindElement("Menu (text)", "xpath", "//*[@text='Menu']");
        tryFindElement("Menu (content-desc)", "xpath", "//*[@content-desc='Menu']");
        tryFindElement("Hamburger menu", "xpath", "//*[@content-desc='open menu']");
        tryFindElement("Three lines", "xpath", "//*[contains(@content-desc,'menu')]");
        
        // Look for login elements
        logger.info("Looking for login elements...");
        tryFindElement("Login (text)", "xpath", "//*[@text='Login']");
        tryFindElement("Login (content-desc)", "xpath", "//*[@content-desc='Login']");
        tryFindElement("Login (contains)", "xpath", "//*[contains(@text,'Login')]");
        tryFindElement("Sign in", "xpath", "//*[@text='Sign in']");
        
        // Look for product elements
        logger.info("Looking for product elements...");
        tryFindElement("Backpack (text)", "xpath", "//*[@text='Sauce Labs Backpack']");
        tryFindElement("Backpack (content-desc)", "xpath", "//*[@content-desc='Sauce Labs Backpack']");
        tryFindElement("Backpack (contains)", "xpath", "//*[contains(@text,'Backpack')]");
        
        // Look for cart elements
        logger.info("Looking for cart elements...");
        tryFindElement("Cart (accessibility)", "accessibility id", "cart badge");
        tryFindElement("Cart (text)", "xpath", "//*[@text='Cart']");
        tryFindElement("Cart (content-desc)", "xpath", "//*[@content-desc='Cart']");
        
        // Get page source to see all elements
        logger.info("📋 Getting page source...");
        sendMcpRequest("tools/call", "{\"name\":\"generate_locators\",\"arguments\":{}}");
        Thread.sleep(5000);
        
        // Clean up
        logger.info("🧹 Cleaning up...");
        writer.close();
        reader.close();
        errorReader.close();
        
        process.destroy();
        if (!process.waitFor(5, TimeUnit.SECONDS)) {
            process.destroyForcibly();
        }
        
        logger.info("=== Element Discovery Complete ===");
    }
    
    private static void tryFindElement(String description, String strategy, String selector) throws IOException, InterruptedException {
        logger.info("  🔍 Trying: {} - {} '{}'", description, strategy, selector);
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"" + strategy + "\",\"selector\":\"" + selector + "\"}}");
        Thread.sleep(2000);
    }
    
    private static void sendMcpRequest(String method, String params) throws IOException {
        String request = "{\"jsonrpc\":\"2.0\",\"id\":\"" + (requestId++) + "\",\"method\":\"" + method + "\",\"params\":" + params + "}";
        writer.write(request);
        writer.newLine();
        writer.flush();
    }
    
    private static void startOutputReaders(BufferedReader reader, BufferedReader errorReader) {
        Thread errorThread = new Thread(() -> {
            try {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    if (line.contains("ERROR") || line.contains("WARN")) {
                        logger.warn("🔴 MCP: {}", line);
                    }
                }
            } catch (IOException e) {
                // Ignore
            }
        });
        errorThread.setDaemon(true);
        errorThread.start();
        
        Thread readerThread = new Thread(() -> {
            try {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (line.trim().startsWith("{")) {
                        if (line.contains("\"result\"") && line.contains("\"content\"")) {
                            logger.info("✅ MCP Result: {}", line.length() > 200 ? line.substring(0, 200) + "..." : line);
                        } else {
                            logger.debug("📨 MCP: {}", line.length() > 100 ? line.substring(0, 100) + "..." : line);
                        }
                    }
                }
            } catch (IOException e) {
                // Ignore
            }
        });
        readerThread.setDaemon(true);
        readerThread.start();
    }
}
