# 🔧 SauceLabs Sample APK Configuration

## ✅ Configuration Complete!

Your examples have been configured for the **SauceLabs Sample App**. Here's what has been set up:

## 📱 APK Details

- **APK Path**: `C:\POC\AI\MCP Integration Server\mcp-client-integration\Android.SauceLabs.Mobile.Sample.app.2.7.1.apk`
- **Package Name**: `com.saucelabs.mydemoapp.android`
- **Main Activity**: `.view.activities.SplashActivity`
- **App Type**: E-commerce demo app with product catalog, cart, and login features

## 🎯 Configured Examples

### 1. **SauceLabsAppTestExample.java** (Recommended)
- **Purpose**: Comprehensive testing of SauceLabs app features
- **Features Tested**:
  - Product catalog display
  - Product selection and details
  - Shopping cart functionality
  - Menu navigation
  - Login/logout features
  - Sort and filter options

### 2. **ApkTestingExample.java** (Updated)
- **Purpose**: General APK testing with SauceLabs-specific elements
- **Features**: Basic app testing workflow

### 3. **CloudApkTestingExample.java** (Updated)
- **Purpose**: Cloud testing on LambdaTest with SauceLabs app

## 🚀 How to Run

### Quick Start (Recommended)
```bash
# Run the comprehensive SauceLabs test
mvn exec:java
```

### Alternative Examples
```bash
# Run basic APK testing
mvn exec:java -Dexec.mainClass="com.jarvis.appium.examples.ApkTestingExample"

# Run cloud testing
mvn exec:java -Dexec.mainClass="com.jarvis.appium.examples.CloudApkTestingExample"

# Run original MCP test
mvn exec:java -Dexec.mainClass="com.jarvis.appium.examples.SingleLineMcpExample"
```

## 🛍️ SauceLabs App Features

The SauceLabs sample app includes these testable features:

### Product Catalog
- **Products**: Sauce Labs Backpack, Bike Light, Bolt T-Shirt, Fleece Jacket, Onesie, Red T-Shirt
- **Sort Options**: Name (A-Z, Z-A), Price (Low-High, High-Low)
- **Product Images**: Each product has an image and description

### Shopping Cart
- **Add to Cart**: Products can be added to cart
- **Cart Badge**: Shows number of items in cart
- **Quantity**: Adjust item quantities
- **Remove Items**: Remove items from cart

### User Authentication
- **Login Screen**: Username and password fields
- **Test Credentials**:
  - Valid: `standard_user` / `secret_sauce`
  - Invalid: Various combinations for error testing
- **Logout**: Available from hamburger menu

### Navigation
- **Hamburger Menu**: Access to different sections
- **Menu Items**: All Items, About, Logout, Reset App State
- **Back Navigation**: Standard Android back button support

## 🔍 Key UI Elements (Accessibility IDs)

### Navigation
- `open menu` - Hamburger menu button
- `cart badge` - Shopping cart icon with item count

### Products
- `Sauce Labs Backpack` - First product item
- `Sauce Labs Bike Light` - Second product item
- `sort button` - Sort/filter button

### Login
- `Username input field` - Username text field
- `Password input field` - Password text field
- `Login button` - Login submit button

### Cart
- `Add To Cart button` - Add product to cart
- `Remove button` - Remove from cart

## 🧪 Test Scenarios Covered

### Functional Testing
1. **App Launch**: Verify app starts correctly
2. **Product Display**: Verify all products are shown
3. **Product Selection**: Test product detail navigation
4. **Cart Operations**: Add/remove items, quantity changes
5. **Login Flow**: Valid and invalid credential testing
6. **Navigation**: Menu navigation and back button

### UI Testing
1. **Element Visibility**: All UI elements are displayed
2. **Responsive Design**: Elements adapt to screen size
3. **Image Loading**: Product images load correctly
4. **Text Display**: All text is readable and correct

### Error Testing
1. **Invalid Login**: Wrong username/password combinations
2. **Empty Cart**: Checkout with empty cart
3. **Network Issues**: Offline behavior (if applicable)

## 📸 Screenshot Points

The test automatically takes screenshots at:
- App launch (splash screen)
- Product catalog view
- Product detail pages
- Shopping cart view
- Login screen
- Menu navigation
- Error states

## 🎯 Expected Test Results

When you run the tests, you should see:

1. **Successful MCP Connection**: Server starts and connects
2. **App Launch**: SauceLabs app opens to product catalog
3. **Element Discovery**: All major UI elements are found
4. **Screenshots**: Multiple screenshots captured
5. **Test Generation**: Automated test code generated
6. **Comprehensive Coverage**: All app features tested

## 🚨 Prerequisites

Before running:

1. **Android Device/Emulator**: Must be connected and running
   ```bash
   adb devices  # Should show your device
   ```

2. **Android SDK**: Properly installed and in PATH

3. **USB Debugging**: Enabled on physical devices

4. **Storage Space**: Ensure device has enough space for app installation

## 🔧 Troubleshooting

### Common Issues

**App doesn't install**:
- Check device storage space
- Verify APK file exists at specified path
- Ensure device allows app installation

**Elements not found**:
- App might still be loading (increase wait times)
- Use `generate_locators` to discover current elements
- Check if app version matches expected UI

**Session creation fails**:
- Verify device is connected: `adb devices`
- Check if another Appium session is running
- Restart device/emulator if needed

## 🎉 Ready to Test!

Your configuration is complete! The SauceLabs sample app is perfect for learning mobile automation because it includes:

- ✅ Real e-commerce workflows
- ✅ Multiple UI element types
- ✅ Login/authentication flows
- ✅ Data entry and validation
- ✅ Navigation patterns
- ✅ Error handling scenarios

Run `mvn exec:java` to start testing! 🚀
