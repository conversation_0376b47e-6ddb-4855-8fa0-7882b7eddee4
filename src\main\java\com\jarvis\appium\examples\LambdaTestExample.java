package com.jarvis.appium.examples;

import com.jarvis.appium.client.JarvisAppiumClient;
import com.jarvis.appium.config.ConfigManager;
import com.jarvis.appium.config.JarvisAppiumConfig;
import com.jarvis.appium.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * LambdaTest cloud testing example
 */
public class LambdaTestExample {
    
    private static final Logger logger = LoggerFactory.getLogger(LambdaTestExample.class);
    
    public static void main(String[] args) {
        try {
            // Load configuration
            JarvisAppiumConfig config = ConfigManager.loadConfig();
            ConfigManager.setupEnvironment(config);
            
            // Create and connect client
            try (JarvisAppiumClient client = new JarvisAppiumClient()) {
                client.connect(config.getServer().getCommand());
                
                // Example 1: Upload app to LambdaTest
                uploadAppExample(client);
                
                // Example 2: Create Android cloud session
                androidCloudSessionExample(client, config);
                
                // Example 3: Create iOS cloud session
                iosCloudSessionExample(client, config);
                
            }
            
        } catch (Exception e) {
            logger.error("LambdaTest example execution failed", e);
        }
    }
    
    /**
     * Upload app to LambdaTest example
     */
    private static void uploadAppExample(JarvisAppiumClient client) throws Exception {
        logger.info("=== Upload App to LambdaTest Example ===");
        
        // Note: Replace with actual app path
        String appPath = "/path/to/your/app.apk";
        String appName = "MyTestApp";
        
        try {
            String appUrl = client.uploadAppToLambdaTest(appPath, appName);
            logger.info("App uploaded successfully: {}", appUrl);
        } catch (Exception e) {
            logger.warn("App upload failed (expected if file doesn't exist): {}", e.getMessage());
        }
    }
    
    /**
     * Android cloud session example
     */
    private static void androidCloudSessionExample(JarvisAppiumClient client, JarvisAppiumConfig config) throws Exception {
        logger.info("=== Android Cloud Session Example ===");
        
        // Configure LambdaTest options
        Map<String, Object> ltOptions = new HashMap<>();
        ltOptions.put("video", true);
        ltOptions.put("visual", true);
        ltOptions.put("network", true);
        ltOptions.put("console", true);
        ltOptions.put("timezone", "UTC");
        
        // Create session configuration
        LambdaTestSessionConfig sessionConfig = new LambdaTestSessionConfig(
            Platform.ANDROID, 
            "Galaxy S21", 
            "11.0"
        )
        .withApp("lt://APP123456789") // Replace with actual app URL from upload
        .withBuildName("Java Client Test Build")
        .withTestName("Android Basic Test")
        .withLtOptions(ltOptions);
        
        try {
            SessionInfo session = client.createLambdaTestSession(sessionConfig);
            logger.info("Created LambdaTest Android session: {}", session);
            
            // Perform some basic interactions
            performBasicInteractions(client);
            
        } catch (Exception e) {
            logger.warn("LambdaTest Android session failed: {}", e.getMessage());
        }
    }
    
    /**
     * iOS cloud session example
     */
    private static void iosCloudSessionExample(JarvisAppiumClient client, JarvisAppiumConfig config) throws Exception {
        logger.info("=== iOS Cloud Session Example ===");
        
        // Configure LambdaTest options
        Map<String, Object> ltOptions = new HashMap<>();
        ltOptions.put("video", true);
        ltOptions.put("visual", true);
        ltOptions.put("network", true);
        ltOptions.put("console", true);
        ltOptions.put("devicelog", true);
        
        // Create session configuration
        LambdaTestSessionConfig sessionConfig = new LambdaTestSessionConfig(
            Platform.IOS, 
            "iPhone 13 Pro", 
            "15.0"
        )
        .withApp("lt://APP987654321") // Replace with actual app URL from upload
        .withBuildName("Java Client Test Build")
        .withTestName("iOS Basic Test")
        .withLtOptions(ltOptions);
        
        try {
            SessionInfo session = client.createLambdaTestSession(sessionConfig);
            logger.info("Created LambdaTest iOS session: {}", session);
            
            // Perform some basic interactions
            performBasicInteractions(client);
            
        } catch (Exception e) {
            logger.warn("LambdaTest iOS session failed: {}", e.getMessage());
        }
    }
    
    /**
     * Perform basic interactions on the app
     */
    private static void performBasicInteractions(JarvisAppiumClient client) throws Exception {
        logger.info("Performing basic interactions...");
        
        // Take initial screenshot
        String screenshot = client.takeScreenshot();
        logger.info("Initial screenshot captured");
        
        // Generate locators
        var locators = client.generateLocators();
        logger.info("Found {} interactive elements", locators.size());
        
        // Try to find and interact with a few elements
        int interactionCount = 0;
        for (ElementLocator locator : locators) {
            if (Boolean.TRUE.equals(locator.getIsClickable()) && interactionCount < 3) {
                ElementLocator.LocatorInfo bestLocator = locator.getBestLocator(client.getCurrentPlatform());
                if (bestLocator != null) {
                    try {
                        String elementId = client.findElement(bestLocator.getStrategy(), bestLocator.getValue());
                        logger.info("Found element: {} (text: '{}')", elementId, locator.getText());
                        
                        // Click the element
                        client.clickElement(elementId);
                        logger.info("Clicked element with text: '{}'", locator.getText());
                        
                        // Take screenshot after interaction
                        client.takeScreenshot();
                        logger.info("Screenshot taken after interaction");
                        
                        interactionCount++;
                        
                        // Wait a bit between interactions
                        Thread.sleep(2000);
                        
                    } catch (Exception e) {
                        logger.debug("Failed to interact with element: {}", e.getMessage());
                    }
                }
            }
        }
        
        logger.info("Completed {} interactions", interactionCount);
    }
}
