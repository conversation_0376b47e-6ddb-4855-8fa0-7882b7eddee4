package com.jarvis.appium.examples;

import com.jarvis.appium.client.JarvisAppiumClient;
import com.jarvis.appium.config.ConfigManager;
import com.jarvis.appium.config.JarvisAppiumConfig;
import com.jarvis.appium.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * Basic usage example for Jarvis Appium Java client
 */
public class BasicUsageExample {
    
    private static final Logger logger = LoggerFactory.getLogger(BasicUsageExample.class);
    
    public static void main(String[] args) {
        try {
            // Load configuration
            JarvisAppiumConfig config = ConfigManager.loadConfig();
            ConfigManager.setupEnvironment(config);
            
            // Create and connect client
            try (JarvisAppiumClient client = new JarvisAppiumClient()) {
                client.connect(config.getServer().getCommand());
                
                // Example 1: Basic Android session
                basicAndroidExample(client);
                
                // Example 2: Element interaction
                elementInteractionExample(client);
                
                // Example 3: Locator generation
                locatorGenerationExample(client);
                
            }
            
        } catch (Exception e) {
            logger.error("Example execution failed", e);
        }
    }
    
    /**
     * Basic Android session example
     */
    private static void basicAndroidExample(JarvisAppiumClient client) throws Exception {
        logger.info("=== Basic Android Session Example ===");
        
        // Select platform
        client.selectPlatform(Platform.ANDROID);
        
        // Create session
        SessionInfo session = client.createSession(Platform.ANDROID);
        logger.info("Created session: {}", session);
        
        // Take a screenshot
        String screenshot = client.takeScreenshot();
        logger.info("Screenshot taken: {}", screenshot.length() > 100 ? 
                   screenshot.substring(0, 100) + "..." : screenshot);
    }
    
    /**
     * Element interaction example
     */
    private static void elementInteractionExample(JarvisAppiumClient client) throws Exception {
        logger.info("=== Element Interaction Example ===");
        
        // Find an element by ID
        try {
            String elementId = client.findElement(LocatorStrategy.ID, "com.example:id/button");
            logger.info("Found element: {}", elementId);
            
            // Click the element
            client.clickElement(elementId);
            logger.info("Clicked element");
            
        } catch (Exception e) {
            logger.warn("Element interaction failed (expected if element doesn't exist): {}", e.getMessage());
        }
        
        // Find a text input and set value
        try {
            String textFieldId = client.findElement(LocatorStrategy.ID, "com.example:id/edittext");
            client.setElementValue(textFieldId, "Hello from Jarvis Appium!");
            logger.info("Set text value");
            
            // Get the text back
            String text = client.getElementText(textFieldId);
            logger.info("Retrieved text: {}", text);
            
        } catch (Exception e) {
            logger.warn("Text input interaction failed (expected if element doesn't exist): {}", e.getMessage());
        }
    }
    
    /**
     * Locator generation example
     */
    private static void locatorGenerationExample(JarvisAppiumClient client) throws Exception {
        logger.info("=== Locator Generation Example ===");
        
        // Generate locators for current screen
        List<ElementLocator> locators = client.generateLocators();
        logger.info("Generated {} locators", locators.size());
        
        // Display first few locators
        int count = Math.min(5, locators.size());
        for (int i = 0; i < count; i++) {
            ElementLocator locator = locators.get(i);
            logger.info("Element {}: clickable={}, focusable={}, text='{}'", 
                       i + 1, locator.getIsClickable(), locator.getIsFocusable(), locator.getText());
            
            // Show best locator for current platform
            ElementLocator.LocatorInfo bestLocator = locator.getBestLocator(client.getCurrentPlatform());
            if (bestLocator != null) {
                logger.info("  Best locator: {} = '{}'", 
                           bestLocator.getStrategy(), bestLocator.getValue());
            }
        }
        
        // Try to interact with clickable elements
        for (ElementLocator locator : locators) {
            if (Boolean.TRUE.equals(locator.getIsClickable())) {
                ElementLocator.LocatorInfo bestLocator = locator.getBestLocator(client.getCurrentPlatform());
                if (bestLocator != null) {
                    try {
                        String elementId = client.findElement(bestLocator.getStrategy(), bestLocator.getValue());
                        logger.info("Found clickable element: {}", elementId);
                        // Note: We're not clicking here to avoid unintended actions
                        break;
                    } catch (Exception e) {
                        logger.debug("Failed to find element: {}", e.getMessage());
                    }
                }
            }
        }
    }
}
