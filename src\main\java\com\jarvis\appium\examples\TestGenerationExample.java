package com.jarvis.appium.examples;

import com.jarvis.appium.client.JarvisAppiumClient;
import com.jarvis.appium.config.ConfigManager;
import com.jarvis.appium.config.JarvisAppiumConfig;
import com.jarvis.appium.model.Platform;
import com.jarvis.appium.test.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Test generation and execution example
 */
public class TestGenerationExample {
    
    private static final Logger logger = LoggerFactory.getLogger(TestGenerationExample.class);
    
    public static void main(String[] args) {
        try {
            // Load configuration
            JarvisAppiumConfig config = ConfigManager.loadConfig();
            ConfigManager.setupEnvironment(config);
            
            // Create and connect client
            try (JarvisAppiumClient client = new JarvisAppiumClient()) {
                client.connect(config.getServer().getCommand());
                
                // Example 1: Manual test scenario creation
                manualTestScenarioExample(client);
                
                // Example 2: AI-generated test scenario
                aiGeneratedTestExample(client);
                
            }
            
        } catch (Exception e) {
            logger.error("Test generation example execution failed", e);
        }
    }
    
    /**
     * Manual test scenario creation and execution
     */
    private static void manualTestScenarioExample(JarvisAppiumClient client) throws Exception {
        logger.info("=== Manual Test Scenario Example ===");
        
        // Create a test scenario
        TestScenario scenario = new TestScenario(
            "Login Test", 
            "Test user login functionality", 
            Platform.ANDROID
        )
        .addTag("login")
        .addTag("authentication")
        .addStep("Take initial screenshot", TestStep.TestAction.SCREENSHOT)
        .addStep("Click username field", TestStep.TestAction.CLICK, "username field")
        .addStep("Enter username", TestStep.TestAction.SET_TEXT, "username field", "<EMAIL>")
        .addStep("Click password field", TestStep.TestAction.CLICK, "password field")
        .addStep("Enter password", TestStep.TestAction.SET_TEXT, "password field", "password123")
        .addStep("Click login button", TestStep.TestAction.CLICK, "login button")
        .addStep("Wait for login", TestStep.TestAction.WAIT, null, "3000")
        .addStep("Verify welcome message", TestStep.TestAction.VERIFY_TEXT, "welcome message", "Welcome")
        .addStep("Take final screenshot", TestStep.TestAction.SCREENSHOT);
        
        logger.info("Created test scenario: {}", scenario);
        
        // Execute the test scenario
        executeTestScenario(client, scenario);
    }
    
    /**
     * AI-generated test scenario example
     */
    private static void aiGeneratedTestExample(JarvisAppiumClient client) throws Exception {
        logger.info("=== AI-Generated Test Example ===");
        
        // Select platform
        client.selectPlatform(Platform.ANDROID);
        
        // Generate test using AI
        String testDescription = "Test the main navigation flow of the app, including opening menus and navigating between screens";
        
        try {
            String generatedTest = client.generateTest(testDescription);
            logger.info("Generated test: {}", generatedTest);
            
            // Parse the generated test into a scenario
            TestScenario aiScenario = parseGeneratedTest(generatedTest, Platform.ANDROID);
            
            if (aiScenario != null) {
                logger.info("Parsed AI-generated scenario: {}", aiScenario);
                executeTestScenario(client, aiScenario);
            }
            
        } catch (Exception e) {
            logger.warn("AI test generation failed: {}", e.getMessage());
        }
    }
    
    /**
     * Execute a test scenario
     */
    private static void executeTestScenario(JarvisAppiumClient client, TestScenario scenario) throws Exception {
        logger.info("Executing test scenario: {}", scenario.getName());
        
        // Create test executor
        TestExecutor executor = new TestExecutor(client);
        
        // Execute the scenario
        TestResult result = executor.executeScenario(scenario);
        
        // Display results
        logger.info("Test execution completed:");
        logger.info("Status: {}", result.getStatus());
        logger.info("Duration: {}ms", result.getDuration());
        logger.info("Steps executed: {}", result.getStepResults().size());
        logger.info("Screenshots taken: {}", result.getScreenshots().size());
        
        if (!result.getErrors().isEmpty()) {
            logger.warn("Errors encountered:");
            for (String error : result.getErrors()) {
                logger.warn("  - {}", error);
            }
        }
        
        // Generate summary report
        String summary = result.generateSummary();
        logger.info("Test Summary:\n{}", summary);
    }
    
    /**
     * Parse generated test into a TestScenario
     * This is a simplified parser - in practice, you'd want more robust parsing
     */
    private static TestScenario parseGeneratedTest(String generatedTest, Platform platform) {
        try {
            // Create a basic scenario from the generated test
            TestScenario scenario = new TestScenario(
                "AI Generated Test",
                "Test generated by AI: " + generatedTest.substring(0, Math.min(100, generatedTest.length())),
                platform
            );
            
            // Add some basic steps based on common patterns
            scenario.addStep("Take initial screenshot", TestStep.TestAction.SCREENSHOT)
                   .addStep("Generate locators", TestStep.TestAction.CUSTOM)
                   .addStep("Interact with first clickable element", TestStep.TestAction.CLICK, "first clickable")
                   .addStep("Take screenshot after interaction", TestStep.TestAction.SCREENSHOT)
                   .addStep("Wait", TestStep.TestAction.WAIT, null, "2000")
                   .addStep("Take final screenshot", TestStep.TestAction.SCREENSHOT);
            
            return scenario;
            
        } catch (Exception e) {
            logger.error("Failed to parse generated test", e);
            return null;
        }
    }
}
