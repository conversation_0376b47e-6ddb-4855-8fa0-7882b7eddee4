package com.jarvis.appium.examples;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.concurrent.TimeUnit;

/**
 * Single-line JSON MCP client example
 */
public class SingleLineMcpExample {
    
    private static final Logger logger = LoggerFactory.getLogger(SingleLineMcpExample.class);
    
    public static void main(String[] args) {
        try {
            testSingleLineMcp();
        } catch (Exception e) {
            logger.error("Single-line MCP test failed", e);
        }
    }
    
    private static void testSingleLineMcp() throws IOException, InterruptedException {
        logger.info("=== Testing Single-Line MCP Client ===");
        
        String[] command = {"node", "C:\\POC\\AI\\MCP Integration Server\\jarvis-appium\\dist\\index.js"};
        
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.redirectErrorStream(false);
        
        Process process = processBuilder.start();
        
        BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(process.getOutputStream()));
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        
        // Start thread to read stderr
        Thread errorThread = new Thread(() -> {
            try {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    logger.debug("STDERR: {}", line);
                }
            } catch (IOException e) {
                logger.debug("Error reading stderr: {}", e.getMessage());
            }
        });
        errorThread.setDaemon(true);
        errorThread.start();
        
        // Start thread to read stdout
        Thread readerThread = new Thread(() -> {
            try {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (line.trim().startsWith("{")) {
                        logger.info("JSON Response: {}", line);
                    } else {
                        logger.debug("Log message: {}", line);
                    }
                }
            } catch (IOException e) {
                logger.debug("Error reading stdout: {}", e.getMessage());
            }
        });
        readerThread.setDaemon(true);
        readerThread.start();
        
        // Wait for server to start
        Thread.sleep(3000);
        
        // Send single-line initialize request with capabilities
        String initRequest = "{\"jsonrpc\":\"2.0\",\"id\":\"init-1\",\"method\":\"initialize\",\"params\":{\"protocolVersion\":\"2024-11-05\",\"clientInfo\":{\"name\":\"jarvis-appium-java-client\",\"version\":\"1.0.0\"},\"capabilities\":{\"roots\":{\"listChanged\":true},\"tools\":{\"listChanged\":true}}}}";
        
        logger.info("Sending initialize request...");
        writer.write(initRequest);
        writer.newLine();
        writer.flush();
        
        // Wait for initialization response
        Thread.sleep(2000);
        
        // Send tools/list request to see available tools
        String toolsListRequest = "{\"jsonrpc\":\"2.0\",\"id\":\"tools-list-1\",\"method\":\"tools/list\",\"params\":{}}";
        
        logger.info("Sending tools/list request...");
        writer.write(toolsListRequest);
        writer.newLine();
        writer.flush();
        
        // Wait for tools list response
        Thread.sleep(2000);
        
        // Send a tool call to test functionality
        String toolCallRequest = "{\"jsonrpc\":\"2.0\",\"id\":\"tool-1\",\"method\":\"tools/call\",\"params\":{\"name\":\"select_platform\",\"arguments\":{\"platform\":\"android\"}}}";
        
        logger.info("Sending tool call request...");
        writer.write(toolCallRequest);
        writer.newLine();
        writer.flush();
        
        // Wait for tool response
        Thread.sleep(3000);
        
        // Clean up
        writer.close();
        reader.close();
        errorReader.close();
        
        process.destroy();
        if (!process.waitFor(5, TimeUnit.SECONDS)) {
            process.destroyForcibly();
        }
        
        logger.info("=== Test Complete ===");
    }
}
