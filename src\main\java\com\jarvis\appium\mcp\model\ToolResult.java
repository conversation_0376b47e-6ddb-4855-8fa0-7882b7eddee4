package com.jarvis.appium.mcp.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

/**
 * Represents the result of a tool execution
 */
public class ToolResult {
    
    @JsonProperty("content")
    private List<ContentItem> content;
    
    @JsonProperty("isError")
    private Boolean isError;
    
    public ToolResult() {}
    
    public ToolResult(List<ContentItem> content) {
        this.content = content;
        this.isError = false;
    }
    
    public ToolResult(List<ContentItem> content, Boolean isError) {
        this.content = content;
        this.isError = isError;
    }
    
    // Getters and setters
    public List<ContentItem> getContent() {
        return content;
    }
    
    public void setContent(List<ContentItem> content) {
        this.content = content;
    }
    
    public Boolean getIsError() {
        return isError;
    }
    
    public void setIsError(Boolean isError) {
        this.isError = isError;
    }
    
    /**
     * Get the text content from the first text content item
     */
    public String getTextContent() {
        if (content != null && !content.isEmpty()) {
            for (ContentItem item : content) {
                if ("text".equals(item.getType())) {
                    return item.getText();
                }
            }
        }
        return null;
    }
    
    @Override
    public String toString() {
        return "ToolResult{" +
                "content=" + content +
                ", isError=" + isError +
                '}';
    }
    
    /**
     * Represents a content item in the tool result
     */
    public static class ContentItem {
        @JsonProperty("type")
        private String type;
        
        @JsonProperty("text")
        private String text;
        
        public ContentItem() {}
        
        public ContentItem(String type, String text) {
            this.type = type;
            this.text = text;
        }
        
        public String getType() {
            return type;
        }
        
        public void setType(String type) {
            this.type = type;
        }
        
        public String getText() {
            return text;
        }
        
        public void setText(String text) {
            this.text = text;
        }
        
        @Override
        public String toString() {
            return "ContentItem{" +
                    "type='" + type + '\'' +
                    ", text='" + text + '\'' +
                    '}';
        }
    }
}
