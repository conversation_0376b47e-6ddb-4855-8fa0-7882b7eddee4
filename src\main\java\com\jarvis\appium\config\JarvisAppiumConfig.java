package com.jarvis.appium.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jarvis.appium.model.Platform;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * Configuration class for Jarvis Appium client
 */
public class JarvisAppiumConfig {
    
    @JsonProperty("server")
    private ServerConfig server = new ServerConfig();
    
    @JsonProperty("capabilities")
    private Map<String, Map<String, Object>> capabilities = new HashMap<>();
    
    @JsonProperty("lambdatest")
    private LambdaTestConfig lambdatest = new LambdaTestConfig();
    
    @JsonProperty("test")
    private TestConfig test = new TestConfig();
    
    public JarvisAppiumConfig() {}
    
    // Getters and setters
    public ServerConfig getServer() {
        return server;
    }
    
    public void setServer(ServerConfig server) {
        this.server = server;
    }
    
    public Map<String, Map<String, Object>> getCapabilities() {
        return capabilities;
    }
    
    public void setCapabilities(Map<String, Map<String, Object>> capabilities) {
        this.capabilities = capabilities;
    }
    
    public Map<String, Object> getCapabilities(Platform platform) {
        return capabilities.get(platform.getValue());
    }
    
    public LambdaTestConfig getLambdatest() {
        return lambdatest;
    }
    
    public void setLambdatest(LambdaTestConfig lambdatest) {
        this.lambdatest = lambdatest;
    }
    
    public TestConfig getTest() {
        return test;
    }
    
    public void setTest(TestConfig test) {
        this.test = test;
    }
    
    /**
     * Load configuration from file
     */
    public static JarvisAppiumConfig fromFile(String filePath) throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.readValue(new File(filePath), JarvisAppiumConfig.class);
    }
    
    /**
     * Load configuration from classpath resource
     */
    public static JarvisAppiumConfig fromResource(String resourcePath) throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        try (InputStream is = JarvisAppiumConfig.class.getClassLoader().getResourceAsStream(resourcePath)) {
            if (is == null) {
                throw new IOException("Resource not found: " + resourcePath);
            }
            return mapper.readValue(is, JarvisAppiumConfig.class);
        }
    }
    
    /**
     * Create default configuration
     */
    public static JarvisAppiumConfig createDefault() {
        JarvisAppiumConfig config = new JarvisAppiumConfig();
        
        // Default Android capabilities
        Map<String, Object> androidCaps = new HashMap<>();
        androidCaps.put("appium:automationName", "UiAutomator2");
        androidCaps.put("appium:deviceName", "Android Device");
        androidCaps.put("appium:platformName", "Android");
        config.capabilities.put("android", androidCaps);
        
        // Default iOS capabilities
        Map<String, Object> iosCaps = new HashMap<>();
        iosCaps.put("appium:automationName", "XCUITest");
        iosCaps.put("appium:deviceName", "iPhone Simulator");
        iosCaps.put("appium:platformName", "iOS");
        config.capabilities.put("ios", iosCaps);
        
        return config;
    }
    
    /**
     * Save configuration to file
     */
    public void saveToFile(String filePath) throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        mapper.writerWithDefaultPrettyPrinter().writeValue(new File(filePath), this);
    }
    
    /**
     * Server configuration
     */
    public static class ServerConfig {
        @JsonProperty("command")
        private String[] command = {"npx", "jarvis-appium"};
        
        @JsonProperty("timeout")
        private int timeout = 30;
        
        @JsonProperty("environment")
        private Map<String, String> environment = new HashMap<>();
        
        public String[] getCommand() {
            return command;
        }
        
        public void setCommand(String[] command) {
            this.command = command;
        }
        
        public int getTimeout() {
            return timeout;
        }
        
        public void setTimeout(int timeout) {
            this.timeout = timeout;
        }
        
        public Map<String, String> getEnvironment() {
            return environment;
        }
        
        public void setEnvironment(Map<String, String> environment) {
            this.environment = environment;
        }
    }
    
    /**
     * LambdaTest configuration
     */
    public static class LambdaTestConfig {
        @JsonProperty("username")
        private String username;
        
        @JsonProperty("accessKey")
        private String accessKey;
        
        @JsonProperty("gridUrl")
        private String gridUrl = "https://mobile-hub.lambdatest.com/wd/hub";
        
        @JsonProperty("defaultOptions")
        private Map<String, Object> defaultOptions = new HashMap<>();
        
        public String getUsername() {
            return username;
        }
        
        public void setUsername(String username) {
            this.username = username;
        }
        
        public String getAccessKey() {
            return accessKey;
        }
        
        public void setAccessKey(String accessKey) {
            this.accessKey = accessKey;
        }
        
        public String getGridUrl() {
            return gridUrl;
        }
        
        public void setGridUrl(String gridUrl) {
            this.gridUrl = gridUrl;
        }
        
        public Map<String, Object> getDefaultOptions() {
            return defaultOptions;
        }
        
        public void setDefaultOptions(Map<String, Object> defaultOptions) {
            this.defaultOptions = defaultOptions;
        }
    }
    
    /**
     * Test configuration
     */
    public static class TestConfig {
        @JsonProperty("timeout")
        private int timeout = 30000;
        
        @JsonProperty("retryCount")
        private int retryCount = 0;
        
        @JsonProperty("screenshotOnFailure")
        private boolean screenshotOnFailure = true;
        
        @JsonProperty("outputDirectory")
        private String outputDirectory = "target/test-results";
        
        public int getTimeout() {
            return timeout;
        }
        
        public void setTimeout(int timeout) {
            this.timeout = timeout;
        }
        
        public int getRetryCount() {
            return retryCount;
        }
        
        public void setRetryCount(int retryCount) {
            this.retryCount = retryCount;
        }
        
        public boolean isScreenshotOnFailure() {
            return screenshotOnFailure;
        }
        
        public void setScreenshotOnFailure(boolean screenshotOnFailure) {
            this.screenshotOnFailure = screenshotOnFailure;
        }
        
        public String getOutputDirectory() {
            return outputDirectory;
        }
        
        public void setOutputDirectory(String outputDirectory) {
            this.outputDirectory = outputDirectory;
        }
    }
}
