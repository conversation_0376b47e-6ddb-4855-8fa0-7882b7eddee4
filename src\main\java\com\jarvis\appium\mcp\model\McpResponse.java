package com.jarvis.appium.mcp.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;

/**
 * Represents an MCP (Model Context Protocol) JSON-RPC response
 */
public class McpResponse {
    
    @JsonProperty("jsonrpc")
    private String jsonrpc = "2.0";
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("result")
    private JsonNode result;
    
    @JsonProperty("error")
    private McpError error;
    
    public McpResponse() {}
    
    public McpResponse(String id, JsonNode result) {
        this.id = id;
        this.result = result;
    }
    
    public McpResponse(String id, McpError error) {
        this.id = id;
        this.error = error;
    }
    
    // Getters and setters
    public String getJsonrpc() {
        return jsonrpc;
    }
    
    public void setJsonrpc(String jsonrpc) {
        this.jsonrpc = jsonrpc;
    }
    
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public JsonNode getResult() {
        return result;
    }
    
    public void setResult(JsonNode result) {
        this.result = result;
    }
    
    public McpError getError() {
        return error;
    }
    
    public void setError(McpError error) {
        this.error = error;
    }
    
    public boolean hasError() {
        return error != null;
    }
    
    @Override
    public String toString() {
        return "McpResponse{" +
                "jsonrpc='" + jsonrpc + '\'' +
                ", id='" + id + '\'' +
                ", result=" + result +
                ", error=" + error +
                '}';
    }
}
