package com.jarvis.appium.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Manages configuration loading and environment setup
 */
public class ConfigManager {
    
    private static final Logger logger = LoggerFactory.getLogger(ConfigManager.class);
    
    private static final String DEFAULT_CONFIG_FILE = "jarvis-appium.json";
    private static final String CONFIG_ENV_VAR = "JARVIS_APPIUM_CONFIG";
    private static final String CAPABILITIES_ENV_VAR = "CAPABILITIES_CONFIG";
    
    private static JarvisAppiumConfig cachedConfig;
    
    /**
     * Load configuration with automatic discovery
     */
    public static JarvisAppiumConfig loadConfig() throws IOException {
        if (cachedConfig != null) {
            return cachedConfig;
        }
        
        // Try environment variable first
        String configPath = System.getenv(CONFIG_ENV_VAR);
        if (configPath != null && Files.exists(Paths.get(configPath))) {
            logger.info("Loading config from environment variable: {}", configPath);
            cachedConfig = JarvisAppiumConfig.fromFile(configPath);
            return cachedConfig;
        }
        
        // Try current directory
        Path currentDirConfig = Paths.get(DEFAULT_CONFIG_FILE);
        if (Files.exists(currentDirConfig)) {
            logger.info("Loading config from current directory: {}", currentDirConfig);
            cachedConfig = JarvisAppiumConfig.fromFile(currentDirConfig.toString());
            return cachedConfig;
        }
        
        // Try user home directory
        Path homeConfig = Paths.get(System.getProperty("user.home"), DEFAULT_CONFIG_FILE);
        if (Files.exists(homeConfig)) {
            logger.info("Loading config from home directory: {}", homeConfig);
            cachedConfig = JarvisAppiumConfig.fromFile(homeConfig.toString());
            return cachedConfig;
        }
        
        // Try classpath
        try {
            logger.info("Loading default config from classpath");
            cachedConfig = JarvisAppiumConfig.fromResource(DEFAULT_CONFIG_FILE);
            return cachedConfig;
        } catch (IOException e) {
            logger.debug("No config found in classpath");
        }
        
        // Create default configuration
        logger.info("Creating default configuration");
        cachedConfig = JarvisAppiumConfig.createDefault();
        return cachedConfig;
    }
    
    /**
     * Load configuration from specific file
     */
    public static JarvisAppiumConfig loadConfig(String configPath) throws IOException {
        logger.info("Loading config from: {}", configPath);
        cachedConfig = JarvisAppiumConfig.fromFile(configPath);
        return cachedConfig;
    }
    
    /**
     * Setup environment variables for MCP server
     */
    public static void setupEnvironment(JarvisAppiumConfig config) {
        // Set up environment variables for the MCP server process
        if (config.getServer().getEnvironment() != null) {
            for (String key : config.getServer().getEnvironment().keySet()) {
                String value = config.getServer().getEnvironment().get(key);
                System.setProperty(key, value);
                logger.debug("Set environment variable: {} = {}", key, value);
            }
        }
        
        // Set capabilities config if available
        String capabilitiesPath = System.getenv(CAPABILITIES_ENV_VAR);
        if (capabilitiesPath == null) {
            // Try to create a temporary capabilities file
            try {
                Path tempCapabilities = createTemporaryCapabilitiesFile(config);
                System.setProperty(CAPABILITIES_ENV_VAR, tempCapabilities.toString());
                logger.info("Created temporary capabilities file: {}", tempCapabilities);
            } catch (IOException e) {
                logger.warn("Failed to create temporary capabilities file", e);
            }
        }
    }
    
    /**
     * Create a temporary capabilities file from config
     */
    private static Path createTemporaryCapabilitiesFile(JarvisAppiumConfig config) throws IOException {
        Path tempFile = Files.createTempFile("jarvis-capabilities", ".json");
        
        // Create capabilities JSON structure
        StringBuilder json = new StringBuilder();
        json.append("{\n");
        
        boolean first = true;
        for (String platform : config.getCapabilities().keySet()) {
            if (!first) {
                json.append(",\n");
            }
            json.append("  \"").append(platform).append("\": {\n");
            
            var caps = config.getCapabilities().get(platform);
            boolean firstCap = true;
            for (String key : caps.keySet()) {
                if (!firstCap) {
                    json.append(",\n");
                }
                Object value = caps.get(key);
                json.append("    \"").append(key).append("\": ");
                if (value instanceof String) {
                    json.append("\"").append(value).append("\"");
                } else {
                    json.append(value);
                }
                firstCap = false;
            }
            
            json.append("\n  }");
            first = false;
        }
        
        json.append("\n}");
        
        Files.write(tempFile, json.toString().getBytes());
        tempFile.toFile().deleteOnExit();
        
        return tempFile;
    }
    
    /**
     * Clear cached configuration
     */
    public static void clearCache() {
        cachedConfig = null;
    }
    
    /**
     * Get cached configuration or load if not cached
     */
    public static JarvisAppiumConfig getConfig() throws IOException {
        return loadConfig();
    }
}
