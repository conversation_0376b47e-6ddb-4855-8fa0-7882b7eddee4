# 🚀 Complete Guide: Testing APK with MCP Server

## 📋 Prerequisites Setup

### 1. Android Environment
```bash
# Ensure Android SDK is installed
# Add to PATH: ANDROID_HOME/tools, ANDROID_HOME/platform-tools

# For local testing:
adb devices  # Should show connected device/emulator
```

### 2. APK Information
You'll need to know:
- **APK file path**: `C:\path\to\your\app.apk`
- **Package name**: `com.example.yourapp` (found in AndroidManifest.xml)
- **Main activity**: `.MainActivity` or full name like `com.example.yourapp.MainActivity`

### 3. LambdaTest Setup (for cloud testing)
- Sign up at [LambdaTest](https://www.lambdatest.com/)
- Get your username and access key
- Set environment variables:
```bash
set LT_USERNAME=your_username
set LT_ACCESS_KEY=your_access_key
```

## 🎯 Testing Options

### Option 1: Local Device Testing

1. **Update Configuration** in `ApkTestingExample.java`:
```java
private static final String APK_PATH = "C:\\Users\\<USER>\\Downloads\\your-app.apk";
private static final String PACKAGE_NAME = "com.yourcompany.yourapp";
private static final String ACTIVITY_NAME = ".MainActivity";
```

2. **Run Local Testing**:
```bash
# Update pom.xml to use ApkTestingExample
mvn exec:java
```

### Option 2: Cloud Testing with LambdaTest

1. **Update Configuration** in `CloudApkTestingExample.java`:
```java
private static final String APK_PATH = "C:\\Users\\<USER>\\Downloads\\your-app.apk";
private static final String DEVICE_NAME = "Galaxy S21";  // or "iPhone 13 Pro"
private static final String PLATFORM_VERSION = "11";    // Android/iOS version
```

2. **Run Cloud Testing**:
```bash
# Update pom.xml to use CloudApkTestingExample
mvn exec:java
```

## 🔄 Complete Testing Workflow

### Phase 1: Setup & Connection
1. **Initialize MCP**: Connect to your Jarvis Appium server
2. **Select Platform**: Choose Android or iOS
3. **Upload APK**: (Cloud only) Upload to LambdaTest storage

### Phase 2: Session Creation
4. **Create Session**: Start Appium session with your APK
5. **App Launch**: Automatically installs and launches your app

### Phase 3: Discovery & Interaction
6. **Take Screenshot**: Capture initial app state
7. **Generate Locators**: Auto-discover UI elements
8. **Find Elements**: Locate buttons, text fields, etc.
9. **Interact**: Click, type, swipe, scroll

### Phase 4: Testing & Validation
10. **Functional Testing**: Test app features
11. **UI Testing**: Verify layouts and navigation
12. **Data Testing**: Test forms and inputs
13. **Error Testing**: Test edge cases

### Phase 5: Documentation
14. **Generate Tests**: Auto-create test code
15. **Documentation**: Query Appium best practices

## 🛠️ Available MCP Tools for APK Testing

### Core Session Management
- `select_platform` - Choose Android/iOS
- `create_session` - Local device session
- `create_lambdatest_session` - Cloud session

### Element Interaction
- `appium_find_element` - Locate UI elements
- `appium_click` - Tap/click elements
- `appium_set_value` - Enter text
- `appium_get_text` - Read element text

### Testing Utilities
- `appium_screenshot` - Capture screens
- `generate_locators` - Auto-discover elements
- `appium_generate_tests` - Create test code
- `appium_documentation_query` - Get help

### Cloud Features
- `upload_app_lambdatest` - Upload APK to cloud
- Advanced device selection and configuration

## 📱 Element Finding Strategies

### By ID (Most Reliable)
```java
// Find by resource ID
"strategy": "id", 
"selector": "com.yourapp:id/login_button"
```

### By Text Content
```java
// Find by visible text
"strategy": "xpath", 
"selector": "//*[@text='Login']"
```

### By Class Name
```java
// Find by UI component type
"strategy": "class name", 
"selector": "android.widget.Button"
```

### By Accessibility ID
```java
// Find by accessibility label
"strategy": "accessibility id", 
"selector": "login-button"
```

### By XPath (Most Flexible)
```java
// Complex queries
"strategy": "xpath", 
"selector": "//android.widget.EditText[@hint='Username']"
```

## 🧪 Example Test Scenarios

### Login Flow Testing
```java
// 1. Find username field
appium_find_element("id", "com.app:id/username")
// 2. Enter username
appium_set_value(elementUUID, "<EMAIL>")
// 3. Find password field
appium_find_element("id", "com.app:id/password")
// 4. Enter password
appium_set_value(elementUUID, "password123")
// 5. Click login button
appium_find_element("id", "com.app:id/login_btn")
appium_click(elementUUID)
// 6. Verify success
appium_screenshot()
```

### Navigation Testing
```java
// Test menu navigation
appium_find_element("accessibility id", "menu-button")
appium_click(elementUUID)
appium_screenshot()

// Test back navigation
appium_find_element("xpath", "//android.widget.ImageButton[@content-desc='Navigate up']")
appium_click(elementUUID)
```

### Form Validation Testing
```java
// Test empty form submission
appium_find_element("id", "com.app:id/submit_btn")
appium_click(elementUUID)
// Check for error messages
appium_find_element("xpath", "//*[contains(@text, 'required')]")
appium_get_text(elementUUID)
```

## 🚨 Common Issues & Solutions

### Issue: APK Not Installing
**Solution**: Check APK path and ensure device has enough storage

### Issue: Elements Not Found
**Solution**: Use `generate_locators` first, then try different strategies

### Issue: App Crashes
**Solution**: Check device logs and app permissions

### Issue: Slow Performance
**Solution**: Increase timeouts and use cloud testing for better performance

## 📊 Best Practices

1. **Always start with `select_platform`**
2. **Use `generate_locators` to discover elements**
3. **Take screenshots at key points**
4. **Use stable locators (ID > text > xpath)**
5. **Add waits between actions**
6. **Test on multiple devices/versions**
7. **Generate test code for reusability**

## 🎯 Quick Start Commands

```bash
# 1. Update your APK path in the example file
# 2. Choose your testing approach:

# For local testing:
mvn exec:java -Dexec.mainClass="com.jarvis.appium.examples.ApkTestingExample"

# For cloud testing:
mvn exec:java -Dexec.mainClass="com.jarvis.appium.examples.CloudApkTestingExample"

# For basic MCP testing:
mvn exec:java -Dexec.mainClass="com.jarvis.appium.examples.SingleLineMcpExample"
```

## 📈 Next Steps

After successful APK testing:
1. **Integrate into CI/CD**: Automate testing in your build pipeline
2. **Expand Test Coverage**: Add more test scenarios
3. **Performance Testing**: Test app performance under load
4. **Cross-Platform**: Test on both Android and iOS
5. **Regression Testing**: Automate testing for new releases
