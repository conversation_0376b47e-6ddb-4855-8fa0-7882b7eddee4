package com.jarvis.appium.test;

/**
 * Represents a single test step in a mobile automation test
 */
public class TestStep {
    
    private final String description;
    private final TestAction action;
    private final String target;
    private final String value;
    private final int order;
    
    public TestStep(String description, TestAction action, String target, String value, int order) {
        this.description = description;
        this.action = action;
        this.target = target;
        this.value = value;
        this.order = order;
    }
    
    public TestStep(String description, TestAction action, String target, int order) {
        this(description, action, target, null, order);
    }
    
    public TestStep(String description, TestAction action, int order) {
        this(description, action, null, null, order);
    }
    
    // Getters
    public String getDescription() {
        return description;
    }
    
    public TestAction getAction() {
        return action;
    }
    
    public String getTarget() {
        return target;
    }
    
    public String getValue() {
        return value;
    }
    
    public int getOrder() {
        return order;
    }
    
    @Override
    public String toString() {
        return "TestStep{" +
                "description='" + description + '\'' +
                ", action=" + action +
                ", target='" + target + '\'' +
                ", value='" + value + '\'' +
                ", order=" + order +
                '}';
    }
    
    /**
     * Enum representing different types of test actions
     */
    public enum TestAction {
        OPEN_APP("Open app"),
        CLICK("Click element"),
        SET_TEXT("Set text"),
        GET_TEXT("Get text"),
        VERIFY_TEXT("Verify text"),
        VERIFY_ELEMENT_PRESENT("Verify element present"),
        VERIFY_ELEMENT_NOT_PRESENT("Verify element not present"),
        WAIT("Wait"),
        SCREENSHOT("Take screenshot"),
        SWIPE("Swipe"),
        SCROLL("Scroll"),
        BACK("Go back"),
        CUSTOM("Custom action");
        
        private final String description;
        
        TestAction(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
        
        @Override
        public String toString() {
            return description;
        }
    }
}
