package com.jarvis.appium.examples;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.concurrent.TimeUnit;

/**
 * Direct test of MCP server communication
 */
public class DirectMcpTestExample {
    
    private static final Logger logger = LoggerFactory.getLogger(DirectMcpTestExample.class);
    
    public static void main(String[] args) {
        try {
            testMcpServerDirectly();
        } catch (Exception e) {
            logger.error("Direct MCP test failed", e);
        }
    }
    
    private static void testMcpServerDirectly() throws IOException, InterruptedException {
        logger.info("=== Testing MCP Server Directly ===");
        
        String[] command = {"node", "C:\\POC\\AI\\MCP Integration Server\\jarvis-appium\\dist\\index.js"};
        
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.redirectErrorStream(false); // Keep stderr separate
        
        Process process = processBuilder.start();
        
        BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(process.getOutputStream()));
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        
        // Start a thread to read stderr (log messages)
        Thread errorThread = new Thread(() -> {
            try {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    logger.info("STDERR: {}", line);
                }
            } catch (IOException e) {
                logger.debug("Error reading stderr: {}", e.getMessage());
            }
        });
        errorThread.setDaemon(true);
        errorThread.start();
        
        // Wait a moment for server to start
        Thread.sleep(2000);
        
        // Send MCP initialize request
        String initRequest = "{\"jsonrpc\":\"2.0\",\"id\":\"test-1\",\"method\":\"initialize\",\"params\":{\"protocolVersion\":\"2024-11-05\",\"clientInfo\":{\"name\":\"test-client\",\"version\":\"1.0.0\"}}}";
        
        logger.info("Sending initialize request: {}", initRequest);
        writer.write(initRequest);
        writer.newLine();
        writer.flush();
        
        // Read response with timeout
        boolean responseReceived = false;
        long startTime = System.currentTimeMillis();
        long timeout = 10000; // 10 seconds
        
        while (!responseReceived && (System.currentTimeMillis() - startTime) < timeout) {
            if (reader.ready()) {
                String response = reader.readLine();
                if (response != null) {
                    logger.info("STDOUT Response: {}", response);
                    
                    // Check if it's a JSON response
                    if (response.trim().startsWith("{")) {
                        logger.info("✓ Received JSON response!");
                        responseReceived = true;
                    } else {
                        logger.warn("✗ Received non-JSON response: {}", response);
                    }
                }
            }
            Thread.sleep(100);
        }
        
        if (!responseReceived) {
            logger.error("✗ No valid JSON response received within timeout");
        }
        
        // Clean up
        writer.close();
        reader.close();
        errorReader.close();
        
        process.destroy();
        if (!process.waitFor(5, TimeUnit.SECONDS)) {
            process.destroyForcibly();
        }
        
        logger.info("=== Test Complete ===");
    }
}
