package com.jarvis.appium.model;

/**
 * Represents information about a mobile automation session
 */
public class SessionInfo {
    
    private final String sessionId;
    private final Platform platform;
    private final String details;
    
    public SessionInfo(String sessionId, Platform platform, String details) {
        this.sessionId = sessionId;
        this.platform = platform;
        this.details = details;
    }
    
    public String getSessionId() {
        return sessionId;
    }
    
    public Platform getPlatform() {
        return platform;
    }
    
    public String getDetails() {
        return details;
    }
    
    @Override
    public String toString() {
        return "SessionInfo{" +
                "sessionId='" + sessionId + '\'' +
                ", platform=" + platform +
                ", details='" + details + '\'' +
                '}';
    }
}
