package com.jarvis.appium.examples;

import com.jarvis.appium.client.HttpJarvisAppiumClient;
import com.jarvis.appium.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * Basic usage example for Jarvis Appium Java HTTP client
 * Connects to MCP server running on port 8080
 */
public class HttpBasicUsageExample {
    
    private static final Logger logger = LoggerFactory.getLogger(HttpBasicUsageExample.class);
    
    public static void main(String[] args) {
        try {
            // Create and connect HTTP client to server on port 8080
            try (HttpJarvisAppiumClient client = new HttpJarvisAppiumClient()) {
                client.connect(8080);
                
                logger.info("Successfully connected to MCP server on port 8080");
                
                // Example 1: Basic Android session
                basicAndroidExample(client);
                
                // Example 2: Element interaction
                elementInteractionExample(client);
                
                // Example 3: Locator generation
                locatorGenerationExample(client);
                
            }
            
        } catch (Exception e) {
            logger.error("Example execution failed", e);
        }
    }
    
    private static void basicAndroidExample(HttpJarvisAppiumClient client) {
        try {
            logger.info("=== Basic Android Example ===");
            
            // Select Android platform
            client.selectPlatform(Platform.ANDROID);
            
            // Create session
            SessionInfo session = client.createSession(Platform.ANDROID);
            logger.info("Created Android session: {}", session);
            
        } catch (Exception e) {
            logger.warn("Basic Android example failed: {}", e.getMessage());
        }
    }
    
    private static void elementInteractionExample(HttpJarvisAppiumClient client) {
        try {
            logger.info("=== Element Interaction Example ===");
            
            // Take a screenshot first
            String screenshot = client.takeScreenshot();
            logger.info("Screenshot taken, length: {} characters", screenshot.length());
            
            // Try to find a common element (this might fail if no app is loaded)
            try {
                String elementId = client.findElement(LocatorStrategy.ID, "android:id/content");
                logger.info("Found element with ID: {}", elementId);
                
                // Click the element
                client.clickElement(elementId);
                logger.info("Clicked element successfully");
                
            } catch (Exception e) {
                logger.info("Element interaction failed (expected if no app loaded): {}", e.getMessage());
            }
            
        } catch (Exception e) {
            logger.warn("Element interaction example failed: {}", e.getMessage());
        }
    }
    
    private static void locatorGenerationExample(HttpJarvisAppiumClient client) {
        try {
            logger.info("=== Locator Generation Example ===");
            
            // Generate locators for current screen
            List<ElementLocator> locators = client.generateLocators();
            logger.info("Generated {} locators", locators.size());
            
            // Display first few locators
            for (int i = 0; i < Math.min(3, locators.size()); i++) {
                ElementLocator locator = locators.get(i);
                logger.info("Locator {}: {}", i + 1, locator);
                
                // Get best locator for Android
                ElementLocator.LocatorInfo bestLocator = locator.getBestLocator(Platform.ANDROID);
                if (bestLocator != null) {
                    logger.info("  Best locator: {} = {}", bestLocator.getStrategy(), bestLocator.getValue());
                }
            }
            
        } catch (Exception e) {
            logger.warn("Locator generation example failed: {}", e.getMessage());
        }
    }
}
