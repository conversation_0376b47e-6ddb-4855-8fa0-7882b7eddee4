@echo off
echo ========================================
echo APK Information Extractor
echo ========================================
echo.

if "%1"=="" (
    echo Usage: get_apk_info.bat "path\to\your\app.apk"
    echo Example: get_apk_info.bat "C:\Downloads\myapp.apk"
    echo.
    pause
    exit /b 1
)

set APK_PATH=%1

echo Analyzing APK: %APK_PATH%
echo.

echo ----------------------------------------
echo Package Information:
echo ----------------------------------------
aapt dump badging "%APK_PATH%" | findstr "package:"
echo.

echo ----------------------------------------
echo Main Activity (Launchable):
echo ----------------------------------------
aapt dump badging "%APK_PATH%" | findstr "launchable-activity:"
echo.

echo ----------------------------------------
echo All Activities:
echo ----------------------------------------
aapt dump badging "%APK_PATH%" | findstr "activity"
echo.

echo ----------------------------------------
echo Permissions:
echo ----------------------------------------
aapt dump badging "%APK_PATH%" | findstr "uses-permission:"
echo.

echo ----------------------------------------
echo SDK Versions:
echo ----------------------------------------
aapt dump badging "%APK_PATH%" | findstr "sdkVersion:"
aapt dump badging "%APK_PATH%" | findstr "targetSdkVersion:"
echo.

echo ========================================
echo Analysis Complete!
echo ========================================
echo.
echo Copy the package name and main activity from above
echo to configure your MCP testing examples.
echo.
pause
