package com.jarvis.appium.examples;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.concurrent.TimeUnit;

/**
 * Complete SauceLabs Sample App testing example
 * Tests the specific features of the SauceLabs demo app
 */
public class SauceLabsAppTestExample {
    
    private static final Logger logger = LoggerFactory.getLogger(SauceLabsAppTestExample.class);
    
    // SauceLabs Sample App Configuration
    private static final String APK_PATH = "C:\\POC\\AI\\MCP Integration Server\\mcp-client-integration\\Android.SauceLabs.Mobile.Sample.app.2.7.1.apk";
    private static final String PACKAGE_NAME = "com.saucelabs.mydemoapp.android";
    private static final String ACTIVITY_NAME = ".view.activities.SplashActivity";
    
    private static BufferedWriter writer;
    private static int requestId = 1;
    
    public static void main(String[] args) {
        try {
            testSauceLabsApp();
        } catch (Exception e) {
            logger.error("SauceLabs app testing failed", e);
        }
    }
    
    private static void testSauceLabsApp() throws IOException, InterruptedException {
        logger.info("=== Testing SauceLabs Sample App ===");
        
        String[] command = {"node", "C:\\POC\\AI\\MCP Integration Server\\jarvis-appium\\dist\\index.js"};
        
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.redirectErrorStream(false);
        
        Process process = processBuilder.start();
        
        writer = new BufferedWriter(new OutputStreamWriter(process.getOutputStream()));
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        
        // Start threads to read output
        startOutputReaders(reader, errorReader);
        
        // Wait for server to start
        Thread.sleep(3000);
        
        // Step 1: Initialize MCP connection
        logger.info("🔗 Step 1: Initializing MCP connection...");
        sendMcpRequest("initialize", "{\"protocolVersion\":\"2024-11-05\",\"clientInfo\":{\"name\":\"jarvis-appium-java-client\",\"version\":\"1.0.0\"},\"capabilities\":{\"roots\":{\"listChanged\":true},\"tools\":{\"listChanged\":true}}}");
        Thread.sleep(2000);
        
        // Step 2: Select Android platform
        logger.info("📱 Step 2: Selecting Android platform...");
        sendMcpRequest("tools/call", "{\"name\":\"select_platform\",\"arguments\":{\"platform\":\"android\"}}");
        Thread.sleep(2000);
        
        // Step 3: Create Android session with SauceLabs APK
        logger.info("🚀 Step 3: Creating Android session with SauceLabs app...");
        String sessionCapabilities = String.format(
            "{\"platform\":\"android\",\"capabilities\":{" +
            "\"platformName\":\"Android\"," +
            "\"app\":\"" + APK_PATH.replace("\\", "\\\\") + "\"," +
            "\"appPackage\":\"" + PACKAGE_NAME + "\"," +
            "\"appActivity\":\"" + ACTIVITY_NAME + "\"," +
            "\"automationName\":\"UiAutomator2\"," +
            "\"newCommandTimeout\":300," +
            "\"autoGrantPermissions\":true" +
            "}}"
        );
        sendMcpRequest("tools/call", "{\"name\":\"create_session\",\"arguments\":" + sessionCapabilities + "}");
        Thread.sleep(8000); // Wait longer for app to launch and splash screen
        
        // Step 4: Take initial screenshot
        logger.info("📸 Step 4: Taking initial screenshot...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(2000);
        
        // Step 5: Generate locators for current screen
        logger.info("🔍 Step 5: Generating locators for product catalog...");
        sendMcpRequest("tools/call", "{\"name\":\"generate_locators\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        // Step 6: Test product catalog functionality
        logger.info("🛍️ Step 6: Testing product catalog...");
        testProductCatalog();
        
        // Step 7: Test product details and cart
        logger.info("🛒 Step 7: Testing product details and cart...");
        testProductDetailsAndCart();
        
        // Step 8: Test menu navigation
        logger.info("📋 Step 8: Testing menu navigation...");
        testMenuNavigation();
        
        // Step 9: Test login functionality
        logger.info("🔐 Step 9: Testing login functionality...");
        testLoginFunctionality();
        
        // Step 10: Generate comprehensive test suite
        logger.info("🧪 Step 10: Generating test suite...");
        generateSauceLabsTestSuite();
        
        // Clean up
        logger.info("🧹 Cleaning up...");
        writer.close();
        reader.close();
        errorReader.close();
        
        process.destroy();
        if (!process.waitFor(5, TimeUnit.SECONDS)) {
            process.destroyForcibly();
        }
        
        logger.info("=== SauceLabs App Testing Complete ===");
    }
    
    private static void testProductCatalog() throws IOException, InterruptedException {
        logger.info("  🔍 Testing product catalog display...");
        
        // Find product items
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@content-desc='Sauce Labs Backpack']\"}}");
        Thread.sleep(2000);
        
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@content-desc='Sauce Labs Bike Light']\"}}");
        Thread.sleep(2000);
        
        // Test sort functionality
        logger.info("  📊 Testing sort functionality...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"accessibility id\",\"selector\":\"sort button\"}}");
        Thread.sleep(2000);
        
        // Take screenshot of catalog
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(2000);
    }
    
    private static void testProductDetailsAndCart() throws IOException, InterruptedException {
        logger.info("  🛍️ Testing product selection and cart...");
        
        // Click on a product (Sauce Labs Backpack)
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@content-desc='Sauce Labs Backpack']\"}}");
        Thread.sleep(2000);
        
        // Look for Add to Cart button
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"accessibility id\",\"selector\":\"Add To Cart button\"}}");
        Thread.sleep(2000);
        
        // Look for cart badge
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@content-desc='cart badge']\"}}");
        Thread.sleep(2000);
        
        // Take screenshot of product details
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(2000);
    }
    
    private static void testMenuNavigation() throws IOException, InterruptedException {
        logger.info("  📋 Testing menu navigation...");
        
        // Find and test hamburger menu
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"accessibility id\",\"selector\":\"open menu\"}}");
        Thread.sleep(2000);
        
        // Look for menu items
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@text='All Items']\"}}");
        Thread.sleep(2000);
        
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@text='About']\"}}");
        Thread.sleep(2000);
        
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@text='Logout']\"}}");
        Thread.sleep(2000);
    }
    
    private static void testLoginFunctionality() throws IOException, InterruptedException {
        logger.info("  🔐 Testing login screen elements...");
        
        // Look for login elements
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"accessibility id\",\"selector\":\"Username input field\"}}");
        Thread.sleep(2000);
        
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"accessibility id\",\"selector\":\"Password input field\"}}");
        Thread.sleep(2000);
        
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"accessibility id\",\"selector\":\"Login button\"}}");
        Thread.sleep(2000);
        
        // Take screenshot of login screen
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(2000);
    }
    
    private static void generateSauceLabsTestSuite() throws IOException, InterruptedException {
        String testSteps = "[" +
            "\"Launch SauceLabs Demo App\"," +
            "\"Verify splash screen and app loading\"," +
            "\"Verify product catalog is displayed\"," +
            "\"Test product sorting functionality\"," +
            "\"Select Sauce Labs Backpack product\"," +
            "\"Verify product details page\"," +
            "\"Add product to shopping cart\"," +
            "\"Verify cart badge updates\"," +
            "\"Navigate to shopping cart\"," +
            "\"Verify product is in cart\"," +
            "\"Test quantity adjustment\"," +
            "\"Open hamburger menu\"," +
            "\"Navigate to login screen\"," +
            "\"Test login with valid credentials\"," +
            "\"Test login with invalid credentials\"," +
            "\"Verify error messages\"," +
            "\"Test logout functionality\"," +
            "\"Navigate back to product catalog\"," +
            "\"Test multiple product selection\"," +
            "\"Test checkout process\"," +
            "\"Verify app navigation flow\"" +
            "]";
        sendMcpRequest("tools/call", "{\"name\":\"appium_generate_tests\",\"arguments\":{\"steps\":" + testSteps + "}}");
        Thread.sleep(3000);
    }
    
    private static void sendMcpRequest(String method, String params) throws IOException {
        String request = "{\"jsonrpc\":\"2.0\",\"id\":\"" + (requestId++) + "\",\"method\":\"" + method + "\",\"params\":" + params + "}";
        logger.debug("Sending: {}", request);
        writer.write(request);
        writer.newLine();
        writer.flush();
    }
    
    private static void startOutputReaders(BufferedReader reader, BufferedReader errorReader) {
        // Thread to read stderr
        Thread errorThread = new Thread(() -> {
            try {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    logger.debug("STDERR: {}", line);
                }
            } catch (IOException e) {
                logger.debug("Error reading stderr: {}", e.getMessage());
            }
        });
        errorThread.setDaemon(true);
        errorThread.start();
        
        // Thread to read stdout
        Thread readerThread = new Thread(() -> {
            try {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (line.trim().startsWith("{")) {
                        logger.info("📨 MCP Response: {}", line);
                    } else {
                        logger.debug("Log: {}", line);
                    }
                }
            } catch (IOException e) {
                logger.debug("Error reading stdout: {}", e.getMessage());
            }
        });
        readerThread.setDaemon(true);
        readerThread.start();
    }
}
