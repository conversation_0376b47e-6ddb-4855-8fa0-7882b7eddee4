package com.jarvis.appium.mcp.client;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jarvis.appium.mcp.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.UUID;

/**
 * HTTP-based MCP client for communicating with the Jarvis Appium MCP server via HTTP
 */
public class HttpMcpClient implements AutoCloseable {
    
    private static final Logger logger = LoggerFactory.getLogger(HttpMcpClient.class);
    private static final int DEFAULT_TIMEOUT_SECONDS = 30;
    
    private final ObjectMapper objectMapper;
    private final HttpClient httpClient;
    private String serverUrl;
    private volatile boolean isConnected = false;
    
    public HttpMcpClient() {
        this.objectMapper = new ObjectMapper();
        this.httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(DEFAULT_TIMEOUT_SECONDS))
                .build();
    }
    
    /**
     * Connect to the MCP server via HTTP
     */
    public void connect(String serverUrl) throws IOException {
        if (isConnected) {
            throw new IllegalStateException("Client is already connected");
        }
        
        this.serverUrl = serverUrl;
        logger.info("Connecting to MCP server at: {}", serverUrl);
        
        // Test connection and initialize
        initialize();
        isConnected = true;
        logger.info("Connected to HTTP MCP server");
    }
    
    /**
     * Initialize the MCP connection
     */
    private void initialize() throws IOException {
        JsonNode initParams = objectMapper.createObjectNode()
                .put("protocolVersion", "2024-11-05")
                .put("clientInfo", objectMapper.createObjectNode()
                        .put("name", "jarvis-appium-java-client")
                        .put("version", "1.0.0"));
        
        McpResponse response = sendRequest("initialize", initParams);
        if (response.hasError()) {
            throw new IOException("Failed to initialize MCP connection: " + response.getError());
        }
        
        logger.info("MCP connection initialized successfully");
    }
    
    /**
     * Send a request to the MCP server
     */
    public McpResponse sendRequest(String method, JsonNode params) throws IOException {
        return sendRequest(method, params, DEFAULT_TIMEOUT_SECONDS);
    }
    
    /**
     * Send a request to the MCP server with timeout
     */
    public McpResponse sendRequest(String method, JsonNode params, int timeoutSeconds) throws IOException {
        if (!isConnected && !"initialize".equals(method)) {
            throw new IllegalStateException("Client is not connected");
        }
        
        String requestId = UUID.randomUUID().toString();
        McpRequest request = new McpRequest(requestId, method, params);
        
        try {
            String requestJson = objectMapper.writeValueAsString(request);
            logger.debug("Sending HTTP request: {}", requestJson);
            
            HttpRequest httpRequest = HttpRequest.newBuilder()
                    .uri(URI.create(serverUrl))
                    .header("Content-Type", "application/json")
                    .timeout(Duration.ofSeconds(timeoutSeconds))
                    .POST(HttpRequest.BodyPublishers.ofString(requestJson))
                    .build();
            
            HttpResponse<String> httpResponse = httpClient.send(httpRequest, 
                    HttpResponse.BodyHandlers.ofString());
            
            if (httpResponse.statusCode() != 200) {
                throw new IOException("HTTP request failed with status: " + httpResponse.statusCode());
            }
            
            String responseJson = httpResponse.body();
            logger.debug("Received HTTP response: {}", responseJson);
            
            return objectMapper.readValue(responseJson, McpResponse.class);
            
        } catch (Exception e) {
            throw new IOException("Failed to send HTTP request: " + e.getMessage(), e);
        }
    }
    
    /**
     * Call a tool on the MCP server
     */
    public ToolResult callTool(String toolName, JsonNode arguments) throws IOException {
        JsonNode params = objectMapper.createObjectNode()
                .put("name", toolName)
                .set("arguments", arguments);
        
        McpResponse response = sendRequest("tools/call", params);
        
        if (response.hasError()) {
            throw new IOException("Tool call failed: " + response.getError().getMessage());
        }
        
        return objectMapper.treeToValue(response.getResult(), ToolResult.class);
    }
    
    /**
     * Check if the client is connected
     */
    public boolean isConnected() {
        return isConnected;
    }
    
    @Override
    public void close() throws IOException {
        if (!isConnected) {
            return;
        }
        
        isConnected = false;
        logger.info("Disconnected from HTTP MCP server");
    }
}
