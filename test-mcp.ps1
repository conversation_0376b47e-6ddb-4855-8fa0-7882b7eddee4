# Simple PowerShell script to test MCP communication
Write-Host "=== Testing MCP Communication ==="

# Start the MCP server
$serverPath = "C:\POC\AI\MCP Integration Server\jarvis-appium"
$serverProcess = Start-Process -FilePath "node" -ArgumentList "dist/index.js" -WorkingDirectory $serverPath -PassThru -NoNewWindow -RedirectStandardInput -RedirectStandardOutput -RedirectStandardError

Write-Host "Started MCP server with PID: $($serverProcess.Id)"

# Wait for server to start
Start-Sleep -Seconds 3

# Test 1: Initialize MCP
$initRequest = '{"jsonrpc":"2.0","id":"1","method":"initialize","params":{"protocolVersion":"2024-11-05","clientInfo":{"name":"powershell-test","version":"1.0.0"},"capabilities":{}}}'
Write-Host "Sending initialize request: $initRequest"

try {
    $serverProcess.StandardInput.WriteLine($initRequest)
    $serverProcess.StandardInput.Flush()
    
    # Wait for response
    Start-Sleep -Seconds 2
    
    # Check if process is still alive
    if ($serverProcess.HasExited) {
        Write-Host "ERROR: Server process has exited with code: $($serverProcess.ExitCode)"
    } else {
        Write-Host "SUCCESS: Server is still running"
    }
    
    # Try to read any output
    if ($serverProcess.StandardOutput.Peek() -ne -1) {
        $output = $serverProcess.StandardOutput.ReadLine()
        Write-Host "Server output: $output"
    }
    
} catch {
    Write-Host "ERROR: Failed to communicate with server: $($_.Exception.Message)"
} finally {
    # Cleanup
    if (-not $serverProcess.HasExited) {
        $serverProcess.Kill()
        Write-Host "Killed server process"
    }
}

Write-Host "Test completed"
