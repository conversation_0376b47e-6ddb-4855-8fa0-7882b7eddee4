package com.jarvis.appium.model;

import java.util.Map;

/**
 * Configuration for creating a LambdaTest cloud session
 */
public class LambdaTestSessionConfig {
    
    private Platform platform;
    private String deviceName;
    private String platformVersion;
    private String app;
    private String buildName;
    private String testName;
    private Map<String, Object> ltOptions;
    private Map<String, Object> capabilities;
    
    public LambdaTestSessionConfig() {}
    
    public LambdaTestSessionConfig(Platform platform, String deviceName, String platformVersion) {
        this.platform = platform;
        this.deviceName = deviceName;
        this.platformVersion = platformVersion;
    }
    
    // Getters and setters
    public Platform getPlatform() {
        return platform;
    }
    
    public void setPlatform(Platform platform) {
        this.platform = platform;
    }
    
    public String getDeviceName() {
        return deviceName;
    }
    
    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }
    
    public String getPlatformVersion() {
        return platformVersion;
    }
    
    public void setPlatformVersion(String platformVersion) {
        this.platformVersion = platformVersion;
    }
    
    public String getApp() {
        return app;
    }
    
    public void setApp(String app) {
        this.app = app;
    }
    
    public String getBuildName() {
        return buildName;
    }
    
    public void setBuildName(String buildName) {
        this.buildName = buildName;
    }
    
    public String getTestName() {
        return testName;
    }
    
    public void setTestName(String testName) {
        this.testName = testName;
    }
    
    public Map<String, Object> getLtOptions() {
        return ltOptions;
    }
    
    public void setLtOptions(Map<String, Object> ltOptions) {
        this.ltOptions = ltOptions;
    }
    
    public Map<String, Object> getCapabilities() {
        return capabilities;
    }
    
    public void setCapabilities(Map<String, Object> capabilities) {
        this.capabilities = capabilities;
    }
    
    // Builder pattern methods
    public LambdaTestSessionConfig withApp(String app) {
        this.app = app;
        return this;
    }
    
    public LambdaTestSessionConfig withBuildName(String buildName) {
        this.buildName = buildName;
        return this;
    }
    
    public LambdaTestSessionConfig withTestName(String testName) {
        this.testName = testName;
        return this;
    }
    
    public LambdaTestSessionConfig withLtOptions(Map<String, Object> ltOptions) {
        this.ltOptions = ltOptions;
        return this;
    }
    
    public LambdaTestSessionConfig withCapabilities(Map<String, Object> capabilities) {
        this.capabilities = capabilities;
        return this;
    }
    
    @Override
    public String toString() {
        return "LambdaTestSessionConfig{" +
                "platform=" + platform +
                ", deviceName='" + deviceName + '\'' +
                ", platformVersion='" + platformVersion + '\'' +
                ", app='" + app + '\'' +
                ", buildName='" + buildName + '\'' +
                ", testName='" + testName + '\'' +
                ", ltOptions=" + ltOptions +
                ", capabilities=" + capabilities +
                '}';
    }
}
