package com.jarvis.appium.examples;

import java.io.*;
import java.util.concurrent.TimeUnit;

/**
 * Basic test to verify MCP communication works - no external dependencies
 */
public class BasicMcpTest {
    
    private static BufferedWriter writer;
    private static BufferedReader reader;
    private static BufferedReader errorReader;
    private static int requestId = 1;
    
    public static void main(String[] args) {
        try {
            testMcpCommunication();
        } catch (Exception e) {
            System.err.println("MCP communication test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testMcpCommunication() throws IOException, InterruptedException {
        System.out.println("=== Basic MCP Communication Test ===");
        
        // Start MCP server
        String[] command = {"node", "dist/index.js"};
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.directory(new File("C:\\POC\\AI\\MCP Integration Server\\jarvis-appium"));
        processBuilder.redirectErrorStream(false);
        
        System.out.println("Starting MCP server...");
        Process process = processBuilder.start();

        writer = new BufferedWriter(new OutputStreamWriter(process.getOutputStream()));
        reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));

        // Start output readers in separate threads
        Thread outputThread = new Thread(() -> {
            try {
                String line;
                while ((line = reader.readLine()) != null) {
                    System.out.println("SERVER OUTPUT: " + line);
                }
            } catch (IOException e) {
                System.err.println("Error reading server output: " + e.getMessage());
            }
        });

        Thread errorThread = new Thread(() -> {
            try {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    System.err.println("SERVER ERROR: " + line);
                }
            } catch (IOException e) {
                System.err.println("Error reading server error: " + e.getMessage());
            }
        });

        outputThread.start();
        errorThread.start();

        // Wait for server to start
        System.out.println("Waiting for server to start...");
        Thread.sleep(5000);
        
        // Check if process is alive before sending requests
        if (!process.isAlive()) {
            System.err.println("Server terminated before we could send requests! Exit code: " + process.exitValue());
            return;
        }

        // Test 1: Initialize MCP
        System.out.println("Test 1: Initializing MCP connection...");
        sendMcpRequest("initialize", "{\"protocolVersion\":\"2024-11-05\",\"clientInfo\":{\"name\":\"basic-test\",\"version\":\"1.0.0\"},\"capabilities\":{}}");
        Thread.sleep(2000);

        // Check if process is still alive after initialization
        if (!process.isAlive()) {
            System.err.println("Server terminated after initialization! Exit code: " + process.exitValue());
            return;
        }

        // Test 2: List tools
        System.out.println("Test 2: Listing available tools...");
        sendMcpRequest("tools/list", "{}");
        Thread.sleep(2000);

        // Check if process is still alive after tools/list
        if (!process.isAlive()) {
            System.err.println("Server terminated after tools/list! Exit code: " + process.exitValue());
            return;
        }

        // Test 3: Simple tool call
        System.out.println("Test 3: Selecting platform...");
        sendMcpRequest("tools/call", "{\"name\":\"select_platform\",\"arguments\":{\"platform\":\"android\"}}");
        Thread.sleep(2000);

        // Test 4: Check if process is still alive
        System.out.println("Test 4: Checking if server is still alive...");
        if (process.isAlive()) {
            System.out.println("Server is still running");
        } else {
            System.err.println("Server has terminated! Exit code: " + process.exitValue());
        }

        // Wait a bit more to see any delayed responses
        System.out.println("Waiting for responses...");
        Thread.sleep(5000);

        // Final status check
        if (process.isAlive()) {
            System.out.println("Server survived all tests!");
        } else {
            System.err.println("Server terminated during testing. Exit code: " + process.exitValue());
        }

        // Cleanup
        System.out.println("Cleaning up...");
        try {
            writer.close();
            reader.close();
            errorReader.close();
            process.destroyForcibly();
        } catch (Exception e) {
            System.err.println("Error during cleanup: " + e.getMessage());
        }
        
        System.out.println("Test completed");
    }
    
    private static void sendMcpRequest(String method, String params) throws IOException {
        String request = "{\"jsonrpc\":\"2.0\",\"id\":\"" + (requestId++) + "\",\"method\":\"" + method + "\",\"params\":" + params + "}";
        System.out.println("SENDING: " + request);
        writer.write(request);
        writer.newLine();
        writer.flush();
    }
}
