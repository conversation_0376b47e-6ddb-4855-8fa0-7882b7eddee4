package com.jarvis.appium.examples;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.concurrent.TimeUnit;

/**
 * Simple test to verify MCP communication works
 */
public class SimpleMcpTest {
    
    private static final Logger logger = LoggerFactory.getLogger(SimpleMcpTest.class);
    
    private static BufferedWriter writer;
    private static BufferedReader reader;
    private static BufferedReader errorReader;
    private static int requestId = 1;
    
    public static void main(String[] args) {
        try {
            testMcpCommunication();
        } catch (Exception e) {
            logger.error("MCP communication test failed", e);
        }
    }
    
    private static void testMcpCommunication() throws IOException, InterruptedException {
        logger.info("=== 🧪 Simple MCP Communication Test ===");
        
        // Start MCP server
        String[] command = {"node", "dist/index.js"};
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.directory(new File("C:\\POC\\AI\\MCP Integration Server\\jarvis-appium"));
        processBuilder.redirectErrorStream(false);
        
        logger.info("🚀 Starting MCP server...");
        Process process = processBuilder.start();
        
        writer = new BufferedWriter(new OutputStreamWriter(process.getOutputStream()));
        reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        
        // Start output readers in separate threads
        Thread outputThread = new Thread(() -> {
            try {
                String line;
                while ((line = reader.readLine()) != null) {
                    logger.info("📥 SERVER OUTPUT: {}", line);
                }
            } catch (IOException e) {
                logger.error("Error reading server output", e);
            }
        });
        
        Thread errorThread = new Thread(() -> {
            try {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    logger.error("❌ SERVER ERROR: {}", line);
                }
            } catch (IOException e) {
                logger.error("Error reading server error", e);
            }
        });
        
        outputThread.start();
        errorThread.start();
        
        // Wait for server to start
        logger.info("⏳ Waiting for server to start...");
        Thread.sleep(5000);
        
        // Test 1: Initialize MCP
        logger.info("🔗 Test 1: Initializing MCP connection...");
        sendMcpRequest("initialize", "{\"protocolVersion\":\"2024-11-05\",\"clientInfo\":{\"name\":\"simple-test\",\"version\":\"1.0.0\"},\"capabilities\":{}}");
        Thread.sleep(2000);
        
        // Test 2: List tools
        logger.info("🛠️ Test 2: Listing available tools...");
        sendMcpRequest("tools/list", "{}");
        Thread.sleep(2000);
        
        // Test 3: Simple tool call
        logger.info("📱 Test 3: Selecting platform...");
        sendMcpRequest("tools/call", "{\"name\":\"select_platform\",\"arguments\":{\"platform\":\"android\"}}");
        Thread.sleep(2000);
        
        // Test 4: Check if process is still alive
        logger.info("💓 Test 4: Checking if server is still alive...");
        if (process.isAlive()) {
            logger.info("✅ Server is still running");
        } else {
            logger.error("❌ Server has terminated! Exit code: {}", process.exitValue());
        }
        
        // Wait a bit more to see any delayed responses
        logger.info("⏳ Waiting for responses...");
        Thread.sleep(5000);
        
        // Cleanup
        logger.info("🧹 Cleaning up...");
        try {
            writer.close();
            reader.close();
            errorReader.close();
            process.destroyForcibly();
        } catch (Exception e) {
            logger.warn("Error during cleanup", e);
        }
        
        logger.info("✅ Test completed");
    }
    
    private static void sendMcpRequest(String method, String params) throws IOException {
        String request = "{\"jsonrpc\":\"2.0\",\"id\":\"" + (requestId++) + "\",\"method\":\"" + method + "\",\"params\":" + params + "}";
        logger.info("📤 SENDING: {}", request);
        writer.write(request);
        writer.newLine();
        writer.flush();
    }
}
