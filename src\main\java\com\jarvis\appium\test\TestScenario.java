package com.jarvis.appium.test;

import com.jarvis.appium.model.Platform;
import java.util.ArrayList;
import java.util.List;

/**
 * Represents a complete test scenario with multiple steps
 */
public class TestScenario {
    
    private final String name;
    private final String description;
    private final Platform platform;
    private final List<TestStep> steps;
    private final List<String> tags;
    
    public TestScenario(String name, String description, Platform platform) {
        this.name = name;
        this.description = description;
        this.platform = platform;
        this.steps = new ArrayList<>();
        this.tags = new ArrayList<>();
    }
    
    // Builder methods
    public TestScenario addStep(TestStep step) {
        steps.add(step);
        return this;
    }
    
    public TestScenario addStep(String description, TestStep.TestAction action, String target, String value) {
        steps.add(new TestStep(description, action, target, value, steps.size() + 1));
        return this;
    }
    
    public TestScenario addStep(String description, TestStep.TestAction action, String target) {
        steps.add(new TestStep(description, action, target, steps.size() + 1));
        return this;
    }
    
    public TestScenario addStep(String description, TestStep.TestAction action) {
        steps.add(new TestStep(description, action, steps.size() + 1));
        return this;
    }
    
    public TestScenario addTag(String tag) {
        tags.add(tag);
        return this;
    }
    
    // Getters
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public Platform getPlatform() {
        return platform;
    }
    
    public List<TestStep> getSteps() {
        return new ArrayList<>(steps);
    }
    
    public List<String> getTags() {
        return new ArrayList<>(tags);
    }
    
    /**
     * Get step descriptions as a list of strings for MCP server
     */
    public List<String> getStepDescriptions() {
        List<String> descriptions = new ArrayList<>();
        for (TestStep step : steps) {
            descriptions.add(step.getDescription());
        }
        return descriptions;
    }
    
    @Override
    public String toString() {
        return "TestScenario{" +
                "name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", platform=" + platform +
                ", steps=" + steps.size() +
                ", tags=" + tags +
                '}';
    }
}
