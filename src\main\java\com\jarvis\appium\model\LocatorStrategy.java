package com.jarvis.appium.model;

/**
 * Represents locator strategies supported by Appium
 */
public enum LocatorStrategy {
    XPATH("xpath"),
    ID("id"),
    NAME("name"),
    CLASS_NAME("class name"),
    ACCESSIBILITY_ID("accessibility id"),
    CSS_SELECTOR("css selector"),
    ANDROID_UIAUTOMATOR("-android uiautomator"),
    IOS_PREDICATE_STRING("-ios predicate string"),
    IOS_CLASS_CHAIN("-ios class chain");
    
    private final String value;
    
    LocatorStrategy(String value) {
        this.value = value;
    }
    
    public String getValue() {
        return value;
    }
    
    public static LocatorStrategy fromString(String value) {
        for (LocatorStrategy strategy : LocatorStrategy.values()) {
            if (strategy.value.equalsIgnoreCase(value)) {
                return strategy;
            }
        }
        throw new IllegalArgumentException("Unknown locator strategy: " + value);
    }
    
    /**
     * Get the priority order for iOS platform
     */
    public static LocatorStrategy[] getIosPriorityOrder() {
        return new LocatorStrategy[] {
            ID,
            ACCESSIBILITY_ID,
            IOS_PREDICATE_STRING,
            IOS_CLASS_CHAIN,
            XPATH,
            CLASS_NAME
        };
    }
    
    /**
     * Get the priority order for Android platform
     */
    public static LocatorStrategy[] getAndroidPriorityOrder() {
        return new LocatorStrategy[] {
            ID,
            ACCESSIBILITY_ID,
            XPATH,
            ANDROID_UIAUTOMATOR,
            CLASS_NAME
        };
    }
    
    @Override
    public String toString() {
        return value;
    }
}
