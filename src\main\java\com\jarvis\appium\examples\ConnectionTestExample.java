package com.jarvis.appium.examples;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.Socket;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.URI;
import java.time.Duration;

/**
 * Simple connection test to check if MCP server is reachable
 */
public class ConnectionTestExample {
    
    private static final Logger logger = LoggerFactory.getLogger(ConnectionTestExample.class);
    
    public static void main(String[] args) {
        testPortConnection(8080);
        testHttpConnection("http://localhost:8080");
        testHttpConnection("http://localhost:8080/mcp");
        testHttpConnection("http://localhost:8080/rpc");
        testHttpConnection("http://localhost:8080/api");
    }
    
    private static void testPortConnection(int port) {
        logger.info("=== Testing Port Connection ===");
        try (Socket socket = new Socket("localhost", port)) {
            logger.info("✓ Port {} is open and accepting connections", port);
        } catch (IOException e) {
            logger.error("✗ Cannot connect to port {}: {}", port, e.getMessage());
        }
    }
    
    private static void testHttpConnection(String url) {
        logger.info("=== Testing HTTP Connection to {} ===", url);
        
        HttpClient client = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(5))
                .build();
        
        // Test GET request
        try {
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .timeout(Duration.ofSeconds(5))
                    .GET()
                    .build();
            
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            logger.info("✓ GET {} - Status: {}, Body length: {}", url, response.statusCode(), response.body().length());
            if (response.body().length() < 200) {
                logger.info("Response body: {}", response.body());
            }
        } catch (Exception e) {
            logger.error("✗ GET {} failed: {}", url, e.getMessage());
        }
        
        // Test POST request (MCP style)
        try {
            String mcpRequest = "{\"jsonrpc\":\"2.0\",\"id\":\"test\",\"method\":\"initialize\",\"params\":{\"protocolVersion\":\"2024-11-05\",\"clientInfo\":{\"name\":\"test-client\",\"version\":\"1.0.0\"}}}";
            
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .header("Content-Type", "application/json")
                    .timeout(Duration.ofSeconds(5))
                    .POST(HttpRequest.BodyPublishers.ofString(mcpRequest))
                    .build();
            
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            logger.info("✓ POST {} - Status: {}, Body length: {}", url, response.statusCode(), response.body().length());
            if (response.body().length() < 200) {
                logger.info("Response body: {}", response.body());
            }
        } catch (Exception e) {
            logger.error("✗ POST {} failed: {}", url, e.getMessage());
        }
    }
}
