{"server": {"command": ["node", "C:\\POC\\AI\\MCP Integration Server\\jarvis-appium\\dist\\index.js"], "timeout": 30, "environment": {"CAPABILITIES_CONFIG": "capabilities.json"}}, "capabilities": {"android": {"appium:automationName": "UiAutomator2", "appium:deviceName": "Android Device", "appium:platformName": "Android", "appium:platformVersion": "11.0", "appium:newCommandTimeout": 300, "appium:noReset": false, "appium:fullReset": false}, "ios": {"appium:automationName": "XCUITest", "appium:deviceName": "iPhone Simulator", "appium:platformName": "iOS", "appium:platformVersion": "15.0", "appium:newCommandTimeout": 300, "appium:noReset": false, "appium:fullReset": false}}, "lambdatest": {"username": "${LAMBDATEST_USERNAME}", "accessKey": "${LAMBDATEST_ACCESS_KEY}", "gridUrl": "https://mobile-hub.lambdatest.com/wd/hub", "defaultOptions": {"video": true, "visual": true, "network": true, "console": true, "timezone": "UTC"}}, "test": {"timeout": 30000, "retryCount": 0, "screenshotOnFailure": true, "outputDirectory": "target/test-results"}}