package com.jarvis.appium.examples;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * File-based Given-When-Then to MCP automation
 * Reads test scenarios from test-scenarios.txt and executes them automatically
 */
public class FileBasedGherkinExample {
    
    private static final Logger logger = LoggerFactory.getLogger(FileBasedGherkinExample.class);
    
    // Configuration
    private static final String SCENARIOS_FILE = "test-scenarios.txt";
    private static final String APK_PATH = "C:\\POC\\AI\\MCP Integration Server\\mcp-client-integration\\Android.SauceLabs.Mobile.Sample.app.2.7.1.apk";
    private static final String PACKAGE_NAME = "com.swaglabsmobileapp";
    private static final String ACTIVITY_NAME = "com.swaglabsmobileapp.SplashActivity";
    
    private static BufferedWriter writer;
    private static int requestId = 1;
    private static String lastElementUUID = null;
    
    public static void main(String[] args) {
        try {
            logger.info("🎯 Reading test scenarios from: {}", SCENARIOS_FILE);
            
            // Read scenarios from file
            List<String> scenarios = readScenariosFromFile();
            
            if (scenarios.isEmpty()) {
                logger.error("❌ No scenarios found in {}. Please add your Given-When-Then scenarios to the file.", SCENARIOS_FILE);
                return;
            }
            
            logger.info("📋 Found {} scenario lines to execute", scenarios.size());
            
            // Execute scenarios with MCP
            executeGherkinScenarios(scenarios);
            
        } catch (Exception e) {
            logger.error("Gherkin execution failed", e);
        }
    }
    
    private static List<String> readScenariosFromFile() throws IOException {
        List<String> scenarios = new ArrayList<>();
        
        if (!Files.exists(Paths.get(SCENARIOS_FILE))) {
            logger.error("❌ Scenarios file not found: {}", SCENARIOS_FILE);
            logger.info("💡 Create the file and add your Given-When-Then scenarios");
            return scenarios;
        }
        
        List<String> lines = Files.readAllLines(Paths.get(SCENARIOS_FILE));
        
        for (String line : lines) {
            line = line.trim();
            
            // Skip comments and empty lines
            if (line.isEmpty() || line.startsWith("#")) {
                continue;
            }
            
            scenarios.add(line);
        }
        
        return scenarios;
    }
    
    private static void executeGherkinScenarios(List<String> scenarios) throws IOException, InterruptedException {
        logger.info("=== 🚀 Executing Your Given-When-Then Scenarios ===");
        
        // Start MCP server using node directly
        String[] command = {"node", "dist/index.js"};
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.directory(new File("C:\\POC\\AI\\MCP Integration Server\\jarvis-appium"));
        processBuilder.redirectErrorStream(false);
        Process process = processBuilder.start();
        
        writer = new BufferedWriter(new OutputStreamWriter(process.getOutputStream()));
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        
        // Start output readers
        startOutputReaders(reader, errorReader);
        Thread.sleep(3000);
        
        // Initialize MCP once
        initializeMcp();
        
        // Process each scenario
        String currentScenario = "";
        int scenarioCount = 0;
        
        for (String line : scenarios) {
            if (line.startsWith("Scenario:")) {
                scenarioCount++;
                currentScenario = line;
                logger.info("");
                logger.info("🎬 [{}/{}] Starting: {}", scenarioCount, countScenarios(scenarios), currentScenario);
                logger.info("" + "=".repeat(60));
                continue;
            }
            
            // Execute the step
            executeGherkinStep(line);
        }
        
        logger.info("");
        logger.info("✅ All {} scenarios completed successfully!", scenarioCount);
        
        // Clean up
        cleanup(process, reader, errorReader);
    }
    
    private static int countScenarios(List<String> scenarios) {
        return (int) scenarios.stream().filter(line -> line.startsWith("Scenario:")).count();
    }
    
    private static void initializeMcp() throws IOException, InterruptedException {
        logger.info("🔗 Initializing MCP connection...");
        sendMcpRequest("initialize", "{\"protocolVersion\":\"2024-11-05\",\"clientInfo\":{\"name\":\"file-gherkin-client\",\"version\":\"1.0.0\"},\"capabilities\":{\"roots\":{\"listChanged\":true},\"tools\":{\"listChanged\":true}}}");
        Thread.sleep(3000);

        logger.info("📱 Selecting Android platform...");
        sendMcpRequest("tools/call", "{\"name\":\"select_platform\",\"arguments\":{\"platform\":\"android\"}}");
        Thread.sleep(3000);

        logger.info("🚀 Creating session with SauceLabs app...");
        logger.info("   📍 APK Path: {}", APK_PATH);
        logger.info("   📦 Package: {}", PACKAGE_NAME);
        logger.info("   🎯 Activity: {}", ACTIVITY_NAME);

        String sessionCapabilities = String.format(
            "{\"platform\":\"android\",\"capabilities\":{" +
            "\"platformName\":\"Android\"," +
            "\"deviceName\":\"emulator-5554\"," +
            "\"app\":\"" + APK_PATH.replace("\\", "\\\\") + "\"," +
            "\"appPackage\":\"" + PACKAGE_NAME + "\"," +
            "\"appActivity\":\"" + ACTIVITY_NAME + "\"," +
            "\"automationName\":\"UiAutomator2\"," +
            "\"newCommandTimeout\":300," +
            "\"autoGrantPermissions\":true," +
            "\"noReset\":false," +
            "\"fullReset\":false" +
            "}}"
        );

        logger.info("   🔧 Session capabilities: {}", sessionCapabilities);
        sendMcpRequest("tools/call", "{\"name\":\"create_session\",\"arguments\":" + sessionCapabilities + "}");

        logger.info("   ⏳ Waiting for app to launch (15 seconds)...");
        Thread.sleep(15000); // Wait longer for app to launch

        logger.info("📸 Taking initial screenshot...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(3000);
    }
    
    private static void executeGherkinStep(String step) throws IOException, InterruptedException {
        logger.info("  🔄 {}", step);
        
        // Parse and execute different types of steps
        if (step.matches(".*app is launched.*")) {
            executeAppLaunch();
        } else if (step.matches(".*view the product catalog.*")) {
            executeViewCatalog();
        } else if (step.matches(".*should see (.+)")) {
            executeVerifyElement(extractText(step, "should see (.+)"));
        } else if (step.matches(".*click on (.+)")) {
            executeClickElement(extractText(step, "click on (.+)"));
        } else if (step.matches(".*click the (.+)")) {
            executeClickElement(extractText(step, "click the (.+)"));
        } else if (step.matches(".*open the menu.*")) {
            executeOpenMenu();
        } else if (step.matches(".*navigate to login.*")) {
            executeNavigateToLogin();
        } else if (step.matches(".*enter username '(.+)'")) {
            executeEnterUsername(extractText(step, "enter username '(.+)'"));
        } else if (step.matches(".*enter password '(.+)'")) {
            executeEnterPassword(extractText(step, "enter password '(.+)'"));
        } else if (step.matches(".*cart badge should show (.+)")) {
            executeVerifyCartBadge(extractText(step, "cart badge should show (.+)"));
        } else if (step.matches(".*should be logged in successfully.*")) {
            executeVerifyLogin();
        } else if (step.matches(".*products should be sorted.*")) {
            executeVerifySort();
        } else if (step.matches(".*select sort by (.+)")) {
            executeSelectSort(extractText(step, "select sort by (.+)"));
        } else {
            logger.warn("    ⚠️ Unknown step pattern - taking screenshot");
            sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
            Thread.sleep(2000);
        }
    }
    
    // Step execution methods
    private static void executeAppLaunch() throws IOException, InterruptedException {
        logger.info("    📱 App should be launched - verifying with screenshot");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(2000);
    }
    
    private static void executeViewCatalog() throws IOException, InterruptedException {
        logger.info("    👀 Viewing product catalog - generating locators");
        sendMcpRequest("tools/call", "{\"name\":\"generate_locators\",\"arguments\":{}}");
        Thread.sleep(3000);
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(2000);
    }
    
    private static void executeVerifyElement(String elementText) throws IOException, InterruptedException {
        logger.info("    ✅ Verifying element: {}", elementText);
        String selector = mapTextToSelector(elementText);
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"" + selector + "\"}}");
        Thread.sleep(2000);
    }
    
    private static void executeClickElement(String elementText) throws IOException, InterruptedException {
        logger.info("    👆 Clicking element: {}", elementText);
        String selector = mapTextToSelector(elementText);
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"" + selector + "\"}}");
        Thread.sleep(2000);
        // Note: In enhanced version, capture UUID and perform actual click
    }
    
    private static void executeOpenMenu() throws IOException, InterruptedException {
        logger.info("    📋 Opening hamburger menu");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"accessibility id\",\"selector\":\"open menu\"}}");
        Thread.sleep(2000);
    }
    
    private static void executeNavigateToLogin() throws IOException, InterruptedException {
        logger.info("    🔐 Navigating to login screen");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@text='Login']\"}}");
        Thread.sleep(2000);
    }
    
    private static void executeEnterUsername(String username) throws IOException, InterruptedException {
        logger.info("    👤 Entering username: {}", username);
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"accessibility id\",\"selector\":\"Username input field\"}}");
        Thread.sleep(2000);
    }
    
    private static void executeEnterPassword(String password) throws IOException, InterruptedException {
        logger.info("    🔒 Entering password: {}", "*".repeat(password.length()));
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"accessibility id\",\"selector\":\"Password input field\"}}");
        Thread.sleep(2000);
    }
    
    private static void executeVerifyCartBadge(String expectedCount) throws IOException, InterruptedException {
        logger.info("    🛒 Verifying cart badge shows: {}", expectedCount);
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@content-desc='cart badge']\"}}");
        Thread.sleep(2000);
    }
    
    private static void executeVerifyLogin() throws IOException, InterruptedException {
        logger.info("    ✅ Verifying successful login");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(2000);
    }
    
    private static void executeVerifySort() throws IOException, InterruptedException {
        logger.info("    📊 Verifying products are sorted");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(2000);
    }
    
    private static void executeSelectSort(String sortOption) throws IOException, InterruptedException {
        logger.info("    📊 Selecting sort option: {}", sortOption);
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@text='Name (A to Z)']\"}}");
        Thread.sleep(2000);
    }
    
    // Helper methods
    private static String extractText(String step, String pattern) {
        Pattern p = Pattern.compile(pattern);
        Matcher m = p.matcher(step);
        return m.find() ? m.group(1) : "";
    }
    
    private static String mapTextToSelector(String text) {
        switch (text.toLowerCase()) {
            case "sauce labs backpack":
                return "//*[@content-desc='Sauce Labs Backpack']";
            case "sauce labs bike light":
                return "//*[@content-desc='Sauce Labs Bike Light']";
            case "sort button":
                return "//*[@content-desc='sort button']";
            case "add to cart button":
                return "//*[@content-desc='Add To Cart button']";
            case "login button":
                return "//*[@content-desc='Login button']";
            default:
                return "//*[@text='" + text + "']";
        }
    }
    
    private static void sendMcpRequest(String method, String params) throws IOException {
        String request = "{\"jsonrpc\":\"2.0\",\"id\":\"" + (requestId++) + "\",\"method\":\"" + method + "\",\"params\":" + params + "}";
        writer.write(request);
        writer.newLine();
        writer.flush();
    }
    
    private static void startOutputReaders(BufferedReader reader, BufferedReader errorReader) {
        Thread errorThread = new Thread(() -> {
            try {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    logger.info("🔴 MCP STDERR: {}", line);
                }
            } catch (IOException e) {
                // Ignore
            }
        });
        errorThread.setDaemon(true);
        errorThread.start();

        Thread readerThread = new Thread(() -> {
            try {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (line.trim().startsWith("{")) {
                        logger.info("📨 MCP Response: {}", line);
                    } else if (!line.trim().isEmpty()) {
                        logger.info("📝 MCP Log: {}", line);
                    }
                }
            } catch (IOException e) {
                // Ignore
            }
        });
        readerThread.setDaemon(true);
        readerThread.start();
    }
    
    private static void cleanup(Process process, BufferedReader reader, BufferedReader errorReader) throws IOException, InterruptedException {
        writer.close();
        reader.close();
        errorReader.close();
        process.destroy();
        if (!process.waitFor(5, TimeUnit.SECONDS)) {
            process.destroyForcibly();
        }
    }
}
