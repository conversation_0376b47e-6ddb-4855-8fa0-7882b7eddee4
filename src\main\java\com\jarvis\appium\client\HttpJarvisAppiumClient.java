package com.jarvis.appium.client;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.jarvis.appium.mcp.client.HttpMcpClient;
import com.jarvis.appium.mcp.model.ToolResult;
import com.jarvis.appium.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.List;

/**
 * High-level HTTP client for Jarvis Appium MCP server
 * Provides convenient methods for mobile automation via HTTP
 */
public class HttpJarvisAppiumClient implements AutoCloseable {
    
    private static final Logger logger = LoggerFactory.getLogger(HttpJarvisAppiumClient.class);
    
    private final HttpMcpClient mcpClient;
    private final ObjectMapper objectMapper;
    private String currentSessionId;
    private Platform currentPlatform;
    
    public HttpJarvisAppiumClient() {
        this.mcpClient = new HttpMcpClient();
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * Connect to the Jarvis Appium MCP server via HTTP
     */
    public void connect(String serverUrl) throws IOException {
        mcpClient.connect(serverUrl);
        logger.info("Connected to Jarvis Appium MCP server at: {}", serverUrl);
    }
    
    /**
     * Connect to the Jarvis Appium MCP server on localhost with specified port
     */
    public void connect(int port) throws IOException {
        connect("http://localhost:" + port);
    }
    
    /**
     * Select the platform for testing
     */
    public void selectPlatform(Platform platform) throws IOException {
        ObjectNode params = objectMapper.createObjectNode();
        params.put("platform", platform.getValue());
        
        ToolResult result = mcpClient.callTool("select_platform", params);
        if (result.getIsError() != null && result.getIsError()) {
            throw new IOException("Failed to select platform: " + result.getTextContent());
        }
        
        this.currentPlatform = platform;
        logger.info("Selected platform: {}", platform);
    }
    
    /**
     * Create a new Appium session
     */
    public SessionInfo createSession(Platform platform) throws IOException {
        ObjectNode params = objectMapper.createObjectNode();
        params.put("platform", platform.getValue());
        
        ToolResult result = mcpClient.callTool("create_session", params);
        if (result.getIsError() != null && result.getIsError()) {
            throw new IOException("Failed to create session: " + result.getTextContent());
        }
        
        SessionInfo sessionInfo = objectMapper.readValue(result.getTextContent(), SessionInfo.class);
        this.currentSessionId = sessionInfo.getSessionId();
        this.currentPlatform = platform;
        
        logger.info("Created session: {}", sessionInfo);
        return sessionInfo;
    }
    
    /**
     * Find an element using the specified locator strategy
     */
    public String findElement(LocatorStrategy strategy, String value) throws IOException {
        ObjectNode params = objectMapper.createObjectNode();
        params.put("strategy", strategy.getValue());
        params.put("value", value);
        
        ToolResult result = mcpClient.callTool("find_element", params);
        if (result.getIsError() != null && result.getIsError()) {
            throw new IOException("Failed to find element: " + result.getTextContent());
        }
        
        JsonNode resultNode = objectMapper.readTree(result.getTextContent());
        return resultNode.get("elementId").asText();
    }
    
    /**
     * Click an element
     */
    public void clickElement(String elementId) throws IOException {
        ObjectNode params = objectMapper.createObjectNode();
        params.put("elementId", elementId);
        
        ToolResult result = mcpClient.callTool("click_element", params);
        if (result.getIsError() != null && result.getIsError()) {
            throw new IOException("Failed to click element: " + result.getTextContent());
        }
        
        logger.info("Clicked element: {}", elementId);
    }
    
    /**
     * Set text value for an element
     */
    public void setElementValue(String elementId, String value) throws IOException {
        ObjectNode params = objectMapper.createObjectNode();
        params.put("elementId", elementId);
        params.put("value", value);
        
        ToolResult result = mcpClient.callTool("set_element_value", params);
        if (result.getIsError() != null && result.getIsError()) {
            throw new IOException("Failed to set element value: " + result.getTextContent());
        }
        
        logger.info("Set element {} value to: {}", elementId, value);
    }
    
    /**
     * Generate intelligent locators for the current screen
     */
    public List<ElementLocator> generateLocators() throws IOException {
        ObjectNode params = objectMapper.createObjectNode();
        
        ToolResult result = mcpClient.callTool("generate_locators", params);
        if (result.getIsError() != null && result.getIsError()) {
            throw new IOException("Failed to generate locators: " + result.getTextContent());
        }
        
        JsonNode resultNode = objectMapper.readTree(result.getTextContent());
        return objectMapper.convertValue(resultNode.get("locators"), 
                objectMapper.getTypeFactory().constructCollectionType(List.class, ElementLocator.class));
    }
    
    /**
     * Take a screenshot
     */
    public String takeScreenshot() throws IOException {
        ObjectNode params = objectMapper.createObjectNode();
        
        ToolResult result = mcpClient.callTool("take_screenshot", params);
        if (result.getIsError() != null && result.getIsError()) {
            throw new IOException("Failed to take screenshot: " + result.getTextContent());
        }
        
        JsonNode resultNode = objectMapper.readTree(result.getTextContent());
        return resultNode.get("screenshot").asText();
    }
    
    /**
     * Get current session ID
     */
    public String getCurrentSessionId() {
        return currentSessionId;
    }
    
    /**
     * Get current platform
     */
    public Platform getCurrentPlatform() {
        return currentPlatform;
    }
    
    /**
     * Check if client is connected
     */
    public boolean isConnected() {
        return mcpClient.isConnected();
    }
    
    @Override
    public void close() throws IOException {
        if (mcpClient != null) {
            mcpClient.close();
        }
        logger.info("Closed Jarvis Appium client");
    }
}
